// 工业自动化类型定义

/**
 * 工业设备类型
 */
export enum DeviceType {
  // 机械设备
  CNC_MACHINE = 'cnc_machine',
  ROBOT_ARM = 'robot_arm',
  CONVEYOR = 'conveyor',
  PRESS = 'press',
  LATHE = 'lathe',
  MILLING_MACHINE = 'milling_machine',
  
  // 传感器
  TEMPERATURE_SENSOR = 'temperature_sensor',
  PRESSURE_SENSOR = 'pressure_sensor',
  VIBRATION_SENSOR = 'vibration_sensor',
  PROXIMITY_SENSOR = 'proximity_sensor',
  VISION_SENSOR = 'vision_sensor',
  
  // 执行器
  SERVO_MOTOR = 'servo_motor',
  STEPPER_MOTOR = 'stepper_motor',
  PNEUMATIC_CYLINDER = 'pneumatic_cylinder',
  HYDRAULIC_CYLINDER = 'hydraulic_cylinder',
  
  // 控制器
  PLC = 'plc',
  HMI = 'hmi',
  SCADA = 'scada',
  DCS = 'dcs'
}

/**
 * 工业通信协议类型
 */
export enum ProtocolType {
  MODBUS_TCP = 'modbus_tcp',
  MODBUS_RTU = 'modbus_rtu',
  OPC_UA = 'opc_ua',
  MQTT = 'mqtt',
  ETHERNET_IP = 'ethernet_ip',
  PROFINET = 'profinet',
  ETHERCAT = 'ethercat',
  CAN_BUS = 'can_bus',
  SERIAL = 'serial'
}

/**
 * 设备状态
 */
export enum DeviceStatus {
  OFFLINE = 'offline',
  ONLINE = 'online',
  RUNNING = 'running',
  STOPPED = 'stopped',
  ERROR = 'error',
  MAINTENANCE = 'maintenance',
  ALARM = 'alarm'
}

/**
 * 数据质量等级
 */
export enum DataQuality {
  GOOD = 'good',
  BAD = 'bad',
  UNCERTAIN = 'uncertain',
  STALE = 'stale'
}

/**
 * 工业设备配置
 */
export interface DeviceConfig {
  id: string;
  name: string;
  type: DeviceType;
  protocol: ProtocolType;
  address: string;
  port?: number;
  parameters: Record<string, any>;
  tags: DeviceTag[];
  metadata?: Record<string, any>;
}

/**
 * 设备标签（数据点）
 */
export interface DeviceTag {
  id: string;
  name: string;
  address: string;
  dataType: DataType;
  accessType: AccessType;
  unit?: string;
  description?: string;
  scaling?: ScalingConfig;
  alarms?: AlarmConfig[];
}

/**
 * 数据类型
 */
export enum DataType {
  BOOLEAN = 'boolean',
  INT16 = 'int16',
  INT32 = 'int32',
  FLOAT = 'float',
  DOUBLE = 'double',
  STRING = 'string',
  BYTE_ARRAY = 'byte_array'
}

/**
 * 访问类型
 */
export enum AccessType {
  READ = 'read',
  WRITE = 'write',
  READ_WRITE = 'read_write'
}

/**
 * 缩放配置
 */
export interface ScalingConfig {
  rawMin: number;
  rawMax: number;
  scaledMin: number;
  scaledMax: number;
  unit?: string;
}

/**
 * 报警配置
 */
export interface AlarmConfig {
  id: string;
  type: AlarmType;
  threshold: number;
  message: string;
  severity: AlarmSeverity;
}

/**
 * 报警类型
 */
export enum AlarmType {
  HIGH = 'high',
  LOW = 'low',
  HIGH_HIGH = 'high_high',
  LOW_LOW = 'low_low',
  DEVIATION = 'deviation',
  RATE_OF_CHANGE = 'rate_of_change'
}

/**
 * 报警严重程度
 */
export enum AlarmSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ALARM = 'alarm',
  CRITICAL = 'critical'
}

/**
 * 工业数据点
 */
export interface IndustrialDataPoint {
  tagId: string;
  deviceId: string;
  timestamp: Date;
  value: any;
  quality: DataQuality;
  metadata?: Record<string, any>;
}

/**
 * 设备连接状态
 */
export interface DeviceConnection {
  deviceId: string;
  protocol: ProtocolType;
  status: DeviceStatus;
  lastConnected?: Date;
  lastDisconnected?: Date;
  errorCount: number;
  lastError?: string;
}

/**
 * 工业协议接口
 */
export interface IndustrialProtocol {
  type: ProtocolType;
  connect(config: DeviceConfig): Promise<DeviceConnection>;
  disconnect(deviceId: string): Promise<void>;
  readTag(deviceId: string, tagId: string): Promise<IndustrialDataPoint>;
  writeTag(deviceId: string, tagId: string, value: any): Promise<boolean>;
  readMultipleTags(deviceId: string, tagIds: string[]): Promise<IndustrialDataPoint[]>;
  subscribe(deviceId: string, tagIds: string[], callback: (data: IndustrialDataPoint[]) => void): Promise<string>;
  unsubscribe(subscriptionId: string): Promise<void>;
}

/**
 * 工厂配置
 */
export interface FactoryConfig {
  id: string;
  name: string;
  description?: string;
  devices: DeviceConfig[];
  productionLines: ProductionLineConfig[];
  layout: FactoryLayout;
  metadata?: Record<string, any>;
}

/**
 * 生产线配置
 */
export interface ProductionLineConfig {
  id: string;
  name: string;
  devices: string[]; // 设备ID列表
  workflow: WorkflowStep[];
  capacity: number;
  efficiency: number;
}

/**
 * 工作流步骤
 */
export interface WorkflowStep {
  id: string;
  name: string;
  deviceId: string;
  duration: number;
  parameters: Record<string, any>;
  nextSteps: string[];
}

/**
 * 工厂布局
 */
export interface FactoryLayout {
  width: number;
  height: number;
  depth: number;
  zones: FactoryZone[];
}

/**
 * 工厂区域
 */
export interface FactoryZone {
  id: string;
  name: string;
  type: ZoneType;
  position: { x: number; y: number; z: number };
  size: { width: number; height: number; depth: number };
  devices: string[];
}

/**
 * 区域类型
 */
export enum ZoneType {
  PRODUCTION = 'production',
  STORAGE = 'storage',
  QUALITY_CONTROL = 'quality_control',
  MAINTENANCE = 'maintenance',
  OFFICE = 'office',
  UTILITY = 'utility'
}

/**
 * 生产数据
 */
export interface ProductionData {
  timestamp: Date;
  lineId: string;
  productId: string;
  quantity: number;
  quality: number;
  efficiency: number;
  downtime: number;
  defects: number;
}

/**
 * 设备性能指标
 */
export interface DevicePerformance {
  deviceId: string;
  timestamp: Date;
  availability: number;
  performance: number;
  quality: number;
  oee: number; // Overall Equipment Effectiveness
  mtbf: number; // Mean Time Between Failures
  mttr: number; // Mean Time To Repair
}
