import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as neo4j from 'neo4j-driver';
import * as _ from 'lodash';
import * as moment from 'moment';

/**
 * 实体类型枚举
 */
export enum EntityType {
  EQUIPMENT = 'Equipment',
  COMPONENT = 'Component',
  PROCESS = 'Process',
  MATERIAL = 'Material',
  PERSON = 'Person',
  SKILL = 'Skill',
  FAULT = 'Fault',
  SOLUTION = 'Solution',
  PROCEDURE = 'Procedure',
  STANDARD = 'Standard',
  DOCUMENT = 'Document',
  EXPERIENCE = 'Experience'
}

/**
 * 关系类型枚举
 */
export enum RelationType {
  CONTAINS = 'CONTAINS',
  REQUIRES = 'REQUIRES',
  CAUSES = 'CAUSES',
  SOLVES = 'SOLVES',
  FOLLOWS = 'FOLLOWS',
  DEPENDS_ON = 'DEPENDS_ON',
  SIMILAR_TO = 'SIMILAR_TO',
  PART_OF = 'PART_OF',
  OPERATES = 'OPERATES',
  MAINTAINS = 'MAINTAINS',
  PRODUCES = 'PRODUCES',
  USES = 'USES',
  KNOWS = 'KNOWS',
  EXPERIENCED_IN = 'EXPERIENCED_IN'
}

/**
 * 知识实体接口
 */
interface KnowledgeEntity {
  id: string;
  type: EntityType;
  name: string;
  properties: Record<string, any>;
  confidence: number;
  source: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 知识关系接口
 */
interface KnowledgeRelation {
  id: string;
  type: RelationType;
  sourceId: string;
  targetId: string;
  properties: Record<string, any>;
  weight: number;
  confidence: number;
  source: string;
  createdAt: Date;
}

/**
 * 推理规则接口
 */
interface InferenceRule {
  id: string;
  name: string;
  description: string;
  condition: string; // Cypher查询条件
  conclusion: string; // 推理结论
  confidence: number;
  priority: number;
  enabled: boolean;
}

/**
 * 查询结果接口
 */
interface QueryResult {
  entities: KnowledgeEntity[];
  relations: KnowledgeRelation[];
  paths: any[];
  confidence: number;
  reasoning: string[];
}

/**
 * 专家建议接口
 */
interface ExpertRecommendation {
  id: string;
  type: 'maintenance' | 'troubleshooting' | 'optimization' | 'training';
  title: string;
  description: string;
  steps: string[];
  confidence: number;
  evidence: string[];
  relatedEntities: string[];
  estimatedTime: number;
  difficulty: 'low' | 'medium' | 'high';
  riskLevel: 'low' | 'medium' | 'high';
}

/**
 * 工业知识图谱服务
 */
@Injectable()
export class KnowledgeGraphService {
  private readonly logger = new Logger(KnowledgeGraphService.name);
  
  // Neo4j数据库连接
  private driver: neo4j.Driver;
  private session: neo4j.Session | null = null;
  
  // 推理规则
  private inferenceRules: Map<string, InferenceRule> = new Map();
  
  // 知识缓存
  private entityCache: Map<string, KnowledgeEntity> = new Map();
  private relationCache: Map<string, KnowledgeRelation> = new Map();
  
  // 统计信息
  private statistics = {
    totalEntities: 0,
    totalRelations: 0,
    totalQueries: 0,
    averageQueryTime: 0,
    inferenceCount: 0
  };

  constructor() {
    this.initializeNeo4jConnection();
    this.loadInferenceRules();
    this.buildIndustrialKnowledgeGraph();
  }

  /**
   * 添加知识实体
   * @param entity 知识实体
   * @returns 实体ID
   */
  async addEntity(entity: Omit<KnowledgeEntity, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const entityId = `entity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const fullEntity: KnowledgeEntity = {
        id: entityId,
        ...entity,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // 存储到Neo4j
      const session = this.getSession();
      const query = `
        CREATE (e:${entity.type} {
          id: $id,
          name: $name,
          properties: $properties,
          confidence: $confidence,
          source: $source,
          createdAt: $createdAt,
          updatedAt: $updatedAt
        })
        RETURN e
      `;
      
      await session.run(query, {
        id: entityId,
        name: entity.name,
        properties: JSON.stringify(entity.properties),
        confidence: entity.confidence,
        source: entity.source,
        createdAt: fullEntity.createdAt.toISOString(),
        updatedAt: fullEntity.updatedAt.toISOString()
      });
      
      // 缓存实体
      this.entityCache.set(entityId, fullEntity);
      this.statistics.totalEntities++;
      
      this.logger.log(`知识实体添加成功: ${entityId} - ${entity.name}`);
      return entityId;
      
    } catch (error) {
      this.logger.error('添加知识实体失败', error);
      throw error;
    }
  }

  /**
   * 添加知识关系
   * @param relation 知识关系
   * @returns 关系ID
   */
  async addRelation(relation: Omit<KnowledgeRelation, 'id' | 'createdAt'>): Promise<string> {
    try {
      const relationId = `relation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const fullRelation: KnowledgeRelation = {
        id: relationId,
        ...relation,
        createdAt: new Date()
      };
      
      // 存储到Neo4j
      const session = this.getSession();
      const query = `
        MATCH (source {id: $sourceId}), (target {id: $targetId})
        CREATE (source)-[r:${relation.type} {
          id: $id,
          properties: $properties,
          weight: $weight,
          confidence: $confidence,
          source: $source,
          createdAt: $createdAt
        }]->(target)
        RETURN r
      `;
      
      await session.run(query, {
        id: relationId,
        sourceId: relation.sourceId,
        targetId: relation.targetId,
        properties: JSON.stringify(relation.properties),
        weight: relation.weight,
        confidence: relation.confidence,
        source: relation.source,
        createdAt: fullRelation.createdAt.toISOString()
      });
      
      // 缓存关系
      this.relationCache.set(relationId, fullRelation);
      this.statistics.totalRelations++;
      
      this.logger.log(`知识关系添加成功: ${relationId} - ${relation.type}`);
      return relationId;
      
    } catch (error) {
      this.logger.error('添加知识关系失败', error);
      throw error;
    }
  }

  /**
   * 智能查询
   * @param query 查询条件
   * @param reasoning 是否启用推理
   * @returns 查询结果
   */
  async intelligentQuery(query: string, reasoning: boolean = true): Promise<QueryResult> {
    try {
      const startTime = Date.now();
      this.statistics.totalQueries++;
      
      // 解析自然语言查询
      const parsedQuery = await this.parseNaturalLanguageQuery(query);
      
      // 构建Cypher查询
      const cypherQuery = await this.buildCypherQuery(parsedQuery);
      
      // 执行查询
      const session = this.getSession();
      const result = await session.run(cypherQuery.query, cypherQuery.parameters);
      
      // 处理查询结果
      const queryResult = await this.processQueryResult(result);
      
      // 执行推理
      if (reasoning) {
        const inferenceResult = await this.performInference(queryResult);
        queryResult.entities.push(...inferenceResult.entities);
        queryResult.relations.push(...inferenceResult.relations);
        queryResult.reasoning = inferenceResult.reasoning;
      }
      
      // 计算置信度
      queryResult.confidence = this.calculateResultConfidence(queryResult);
      
      // 更新统计
      const queryTime = Date.now() - startTime;
      this.statistics.averageQueryTime = 
        (this.statistics.averageQueryTime * (this.statistics.totalQueries - 1) + queryTime) / 
        this.statistics.totalQueries;
      
      this.logger.log(`智能查询完成: ${query} - 耗时: ${queryTime}ms`);
      return queryResult;
      
    } catch (error) {
      this.logger.error('智能查询失败', error);
      throw error;
    }
  }

  /**
   * 获取专家建议
   * @param context 上下文信息
   * @param problemType 问题类型
   * @returns 专家建议列表
   */
  async getExpertRecommendations(context: any, problemType: string): Promise<ExpertRecommendation[]> {
    try {
      this.logger.log(`获取专家建议: ${problemType}`);
      
      // 分析上下文
      const contextAnalysis = await this.analyzeContext(context);
      
      // 查找相关知识
      const relevantKnowledge = await this.findRelevantKnowledge(contextAnalysis, problemType);
      
      // 生成建议
      const recommendations = await this.generateRecommendations(relevantKnowledge, contextAnalysis);
      
      // 排序和过滤
      const sortedRecommendations = recommendations
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, 10); // 返回前10个建议
      
      this.logger.log(`专家建议生成完成: ${sortedRecommendations.length} 条建议`);
      return sortedRecommendations;
      
    } catch (error) {
      this.logger.error('获取专家建议失败', error);
      throw error;
    }
  }

  /**
   * 知识推理
   * @param facts 已知事实
   * @returns 推理结果
   */
  async performKnowledgeInference(facts: any[]): Promise<any> {
    try {
      this.logger.log(`开始知识推理: ${facts.length} 个事实`);
      
      const inferenceResults = [];
      const appliedRules = [];
      
      // 应用推理规则
      for (const [ruleId, rule] of this.inferenceRules) {
        if (!rule.enabled) continue;
        
        const ruleResult = await this.applyInferenceRule(rule, facts);
        if (ruleResult.applied) {
          inferenceResults.push(...ruleResult.conclusions);
          appliedRules.push(ruleId);
          this.statistics.inferenceCount++;
        }
      }
      
      // 递归推理（最多3层）
      let depth = 0;
      let newFacts = [...facts, ...inferenceResults];
      
      while (depth < 3) {
        const additionalInferences = [];
        
        for (const [ruleId, rule] of this.inferenceRules) {
          if (!rule.enabled || appliedRules.includes(ruleId)) continue;
          
          const ruleResult = await this.applyInferenceRule(rule, newFacts);
          if (ruleResult.applied) {
            additionalInferences.push(...ruleResult.conclusions);
            appliedRules.push(ruleId);
          }
        }
        
        if (additionalInferences.length === 0) break;
        
        inferenceResults.push(...additionalInferences);
        newFacts.push(...additionalInferences);
        depth++;
      }
      
      const result = {
        originalFacts: facts,
        inferences: inferenceResults,
        appliedRules: appliedRules,
        confidence: this.calculateInferenceConfidence(inferenceResults),
        depth: depth
      };
      
      this.logger.log(`知识推理完成: ${inferenceResults.length} 个推理结果`);
      return result;
      
    } catch (error) {
      this.logger.error('知识推理失败', error);
      throw error;
    }
  }

  /**
   * 相似性搜索
   * @param entity 目标实体
   * @param threshold 相似度阈值
   * @returns 相似实体列表
   */
  async findSimilarEntities(entity: KnowledgeEntity, threshold: number = 0.7): Promise<KnowledgeEntity[]> {
    try {
      const session = this.getSession();
      
      // 基于属性相似性的查询
      const query = `
        MATCH (e:${entity.type})
        WHERE e.id <> $entityId
        RETURN e,
        CASE 
          WHEN e.name CONTAINS $name OR $name CONTAINS e.name THEN 0.8
          ELSE 0.0
        END +
        CASE 
          WHEN size(keys(e.properties)) > 0 THEN 0.2
          ELSE 0.0
        END as similarity
        ORDER BY similarity DESC
        LIMIT 20
      `;
      
      const result = await session.run(query, {
        entityId: entity.id,
        name: entity.name
      });
      
      const similarEntities: KnowledgeEntity[] = [];
      
      for (const record of result.records) {
        const similarity = record.get('similarity');
        if (similarity >= threshold) {
          const entityNode = record.get('e');
          const similarEntity = this.nodeToEntity(entityNode);
          similarEntities.push(similarEntity);
        }
      }
      
      this.logger.log(`相似性搜索完成: 找到 ${similarEntities.length} 个相似实体`);
      return similarEntities;
      
    } catch (error) {
      this.logger.error('相似性搜索失败', error);
      throw error;
    }
  }

  /**
   * 路径查找
   * @param sourceId 源实体ID
   * @param targetId 目标实体ID
   * @param maxDepth 最大深度
   * @returns 路径列表
   */
  async findPaths(sourceId: string, targetId: string, maxDepth: number = 5): Promise<any[]> {
    try {
      const session = this.getSession();
      
      const query = `
        MATCH path = (source {id: $sourceId})-[*1..${maxDepth}]-(target {id: $targetId})
        RETURN path, length(path) as pathLength
        ORDER BY pathLength ASC
        LIMIT 10
      `;
      
      const result = await session.run(query, { sourceId, targetId });
      
      const paths = result.records.map(record => {
        const path = record.get('path');
        const pathLength = record.get('pathLength');
        
        return {
          length: pathLength,
          nodes: path.segments.map((segment: any) => ({
            start: this.nodeToEntity(segment.start),
            relationship: this.relationshipToRelation(segment.relationship),
            end: this.nodeToEntity(segment.end)
          }))
        };
      });
      
      this.logger.log(`路径查找完成: 找到 ${paths.length} 条路径`);
      return paths;
      
    } catch (error) {
      this.logger.error('路径查找失败', error);
      throw error;
    }
  }

  /**
   * 获取知识图谱统计信息
   * @returns 统计信息
   */
  async getStatistics(): Promise<any> {
    try {
      const session = this.getSession();
      
      // 获取节点统计
      const nodeStatsQuery = `
        MATCH (n)
        RETURN labels(n) as labels, count(n) as count
      `;
      const nodeResult = await session.run(nodeStatsQuery);
      
      // 获取关系统计
      const relationStatsQuery = `
        MATCH ()-[r]->()
        RETURN type(r) as type, count(r) as count
      `;
      const relationResult = await session.run(relationStatsQuery);
      
      const nodeStats = nodeResult.records.map(record => ({
        type: record.get('labels')[0],
        count: record.get('count').toNumber()
      }));
      
      const relationStats = relationResult.records.map(record => ({
        type: record.get('type'),
        count: record.get('count').toNumber()
      }));
      
      return {
        ...this.statistics,
        nodeStats,
        relationStats,
        cacheSize: {
          entities: this.entityCache.size,
          relations: this.relationCache.size
        },
        inferenceRules: this.inferenceRules.size
      };
      
    } catch (error) {
      this.logger.error('获取统计信息失败', error);
      throw error;
    }
  }

  /**
   * 初始化Neo4j连接
   */
  private initializeNeo4jConnection(): void {
    try {
      this.driver = neo4j.driver(
        'bolt://localhost:7687',
        neo4j.auth.basic('neo4j', 'password')
      );
      
      this.logger.log('Neo4j连接初始化完成');
      
    } catch (error) {
      this.logger.error('初始化Neo4j连接失败', error);
    }
  }

  /**
   * 获取Neo4j会话
   */
  private getSession(): neo4j.Session {
    if (!this.session) {
      this.session = this.driver.session();
    }
    return this.session;
  }

  /**
   * 加载推理规则
   */
  private loadInferenceRules(): void {
    // 设备故障推理规则
    this.inferenceRules.set('fault_inference_001', {
      id: 'fault_inference_001',
      name: '温度异常故障推理',
      description: '当设备温度异常时，推理可能的故障原因',
      condition: 'MATCH (d:Equipment)-[:HAS_SYMPTOM]->(s:Symptom {type: "temperature_high"})',
      conclusion: 'CREATE (d)-[:LIKELY_HAS_FAULT]->(f:Fault {type: "cooling_system_failure"})',
      confidence: 0.8,
      priority: 1,
      enabled: true
    });
    
    // 维护经验推理规则
    this.inferenceRules.set('maintenance_inference_001', {
      id: 'maintenance_inference_001',
      name: '维护经验推理',
      description: '基于历史维护记录推理最佳维护方案',
      condition: 'MATCH (e:Equipment)-[:SIMILAR_TO]->(se:Equipment)-[:MAINTAINED_BY]->(p:Procedure)',
      conclusion: 'CREATE (e)-[:RECOMMENDED_MAINTENANCE]->(p)',
      confidence: 0.7,
      priority: 2,
      enabled: true
    });
    
    this.logger.log(`推理规则加载完成: ${this.inferenceRules.size} 条规则`);
  }
