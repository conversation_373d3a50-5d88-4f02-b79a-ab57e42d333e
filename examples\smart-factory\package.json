{"name": "smart-factory-example", "version": "1.0.0", "description": "基于DL引擎的智慧工厂应用示例", "main": "dist/main.js", "scripts": {"start": "ts-node src/main.ts", "build": "tsc", "dev": "ts-node --watch src/main.ts", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["smart-factory", "industrial-automation", "iot", "digital-twin", "manufacturing", "dl-engine"], "author": "DL Engine Team", "license": "MIT", "dependencies": {"lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.0"}, "devDependencies": {"@types/lodash": "^4.14.195", "@types/node": "^20.3.1", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "jest": "^29.5.0", "rimraf": "^5.0.1", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.1.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts"]}}