/**
 * 场景理解节点
 */
import { AsyncNode } from './AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from './Node';

export class SceneUnderstandingNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/scene/understand',
      category: NodeCategory.AI,
      name: '场景理解',
      description: '理解自然语言中的场景描述'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addSocket({
      name: 'text',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '输入文本',
      optional: false
    });

    // 输出插槽
    this.addSocket({
      name: 'entities',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: '识别的实体'
    });

    this.addSocket({
      name: 'sentiment',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '情感倾向'
    });

    this.addSocket({
      name: 'intent',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '意图'
    });

    this.addSocket({
      name: 'keywords',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: '关键词'
    });

    this.addSocket({
      name: 'style',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '推断风格'
    });

    // 流程插槽
    this.addSocket({
      name: 'success',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '理解成功'
    });

    this.addSocket({
      name: 'error',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '理解失败'
    });
  }

  async executeAsync(inputs: Record<string, any>): Promise<any> {
    const text = inputs.text as string;

    if (!text || text.trim().length === 0) {
      this.setOutputValue('error', '输入文本不能为空');
      this.triggerFlow('error');
      return { success: false, error: '输入文本不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      // 调用理解方法（需要在NLPSceneGenerator中暴露）
      const understanding = await (nlpGenerator as any).understandText(text);

      // 设置输出值
      this.setOutputValue('entities', understanding.entities);
      this.setOutputValue('sentiment', understanding.sentiment);
      this.setOutputValue('intent', understanding.intent);
      this.setOutputValue('keywords', understanding.keywords);
      this.setOutputValue('style', understanding.style);

      this.triggerFlow('success');
      return { success: true, understanding };

    } catch (error: any) {
      console.error('文本理解失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  getConfiguration(): any {
    return {
      color: '#52C41A',
      icon: 'eye',
      category: 'AI',
      tags: ['nlp', 'understanding', 'analysis'],
      examples: [
        {
          name: '基础文本理解',
          description: '理解简单的场景描述',
          inputs: {
            text: '创建一个现代办公室'
          }
        },
        {
          name: '复杂文本理解',
          description: '理解详细的场景描述',
          inputs: {
            text: '设计一个温馨舒适的咖啡厅，有木质桌椅和绿色植物'
          }
        }
      ]
    };
  }
}
