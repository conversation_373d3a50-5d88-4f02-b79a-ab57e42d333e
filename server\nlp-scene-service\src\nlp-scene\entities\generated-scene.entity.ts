/**
 * 生成场景实体
 */
import {
  Entity, PrimaryGeneratedColumn, Column,
  CreateDateColumn, UpdateDateColumn, Index
} from 'typeorm';

@Entity('generated_scenes')
@Index(['userId', 'createdAt'])
@Index(['style'])
export class GeneratedScene {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('text')
  inputText: string;

  @Column('varchar', { length: 50 })
  style: string;

  @Column('int')
  quality: number;

  @Column('int')
  maxObjects: number;

  @Column('varchar', { length: 100 })
  userId: string;

  @Column('varchar', { length: 100, nullable: true })
  projectId: string;

  @Column('json')
  sceneData: any;

  @Column('json', { nullable: true })
  understanding: any;

  @Column('varchar', { length: 500, nullable: true })
  sceneUrl: string;

  @Column('int', { default: 0 })
  objectCount: number;

  @Column('int', { default: 0 })
  polygonCount: number;

  @Column('int', { default: 0 })
  generationTime: number;

  @Column('boolean', { default: false })
  isPublic: boolean;

  @Column('json', { nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
