/**
 * 生成历史组件
 * 用于管理和显示自然语言场景生成的历史记录
 */
import React from 'react';
import { List, Button, Space, Typography, Tag, Tooltip, Popconfirm, Empty } from 'antd';
import {
  EyeOutlined, ReloadOutlined, DeleteOutlined,
  ClockCircleOutlined, SettingOutlined, StarOutlined,
  StarFilled, ShareAltOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './GenerationHistory.less';

const { Text, Paragraph } = Typography;

interface GenerationRecord {
  id: string;
  text: string;
  style: string;
  quality: number;
  maxObjects: number;
  timestamp: Date;
  scene?: any;
  understanding?: any;
  metadata?: {
    generationTime: number;
    objectCount: number;
    polygonCount: number;
  };
  isFavorite?: boolean;
}

interface GenerationHistoryProps {
  history: GenerationRecord[];
  onPreview: (item: GenerationRecord) => void;
  onRegenerate: (item: GenerationRecord) => void;
  onDelete: (id: string) => void;
  onToggleFavorite?: (id: string) => void;
  onShare?: (item: GenerationRecord) => void;
  maxItems?: number;
}

const GenerationHistory: React.FC<GenerationHistoryProps> = ({
  history,
  onPreview,
  onRegenerate,
  onDelete,
  onToggleFavorite,
  onShare,
  maxItems = 10
}) => {
  const { t } = useTranslation();

  // 获取风格颜色
  const getStyleColor = (style: string): string => {
    const colorMap: { [key: string]: string } = {
      'realistic': 'blue',
      'cartoon': 'orange',
      'minimalist': 'green',
      'scifi': 'purple',
      'fantasy': 'magenta'
    };
    return colorMap[style] || 'default';
  };

  // 获取风格图标
  const getStyleIcon = (style: string): string => {
    const iconMap: { [key: string]: string } = {
      'realistic': '🏢',
      'cartoon': '🎨',
      'minimalist': '⚪',
      'scifi': '🚀',
      'fantasy': '🏰'
    };
    return iconMap[style] || '📦';
  };

  // 获取质量标签
  const getQualityLabel = (quality: number): { label: string; color: string } => {
    if (quality >= 80) return { label: '高', color: 'success' };
    if (quality >= 50) return { label: '中', color: 'warning' };
    return { label: '低', color: 'default' };
  };

  // 格式化生成时间
  const formatGenerationTime = (time: number): string => {
    if (time < 1000) return `${time}ms`;
    if (time < 60000) return `${(time / 1000).toFixed(1)}s`;
    return `${(time / 60000).toFixed(1)}min`;
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    
    if (diff < 60000) return '刚刚';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
    if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;
    
    return timestamp.toLocaleDateString();
  };

  if (history.length === 0) {
    return (
      <div className="generation-history-empty">
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无生成历史"
        />
      </div>
    );
  }

  const displayHistory = history.slice(0, maxItems);

  return (
    <div className="generation-history">
      <List
        size="small"
        dataSource={displayHistory}
        renderItem={(item) => {
          const qualityInfo = getQualityLabel(item.quality);
          
          return (
            <List.Item
              className="history-item"
              actions={[
                <Tooltip title="预览场景">
                  <Button
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={() => onPreview(item)}
                    disabled={!item.scene}
                  />
                </Tooltip>,
                <Tooltip title="重新生成">
                  <Button
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={() => onRegenerate(item)}
                  />
                </Tooltip>,
                onToggleFavorite && (
                  <Tooltip title={item.isFavorite ? "取消收藏" : "收藏"}>
                    <Button
                      size="small"
                      icon={item.isFavorite ? <StarFilled /> : <StarOutlined />}
                      onClick={() => onToggleFavorite(item.id)}
                      type={item.isFavorite ? "primary" : "default"}
                    />
                  </Tooltip>
                ),
                onShare && (
                  <Tooltip title="分享">
                    <Button
                      size="small"
                      icon={<ShareAltOutlined />}
                      onClick={() => onShare(item)}
                    />
                  </Tooltip>
                ),
                <Popconfirm
                  title="确认删除此记录？"
                  onConfirm={() => onDelete(item.id)}
                  okText="删除"
                  cancelText="取消"
                  placement="topRight"
                >
                  <Tooltip title="删除记录">
                    <Button
                      size="small"
                      icon={<DeleteOutlined />}
                      danger
                    />
                  </Tooltip>
                </Popconfirm>
              ].filter(Boolean)}
            >
              <List.Item.Meta
                title={
                  <div className="history-item-title">
                    <Paragraph
                      ellipsis={{ rows: 2, tooltip: item.text }}
                      style={{ margin: 0, maxWidth: 200 }}
                    >
                      {item.text}
                    </Paragraph>
                    <Space size="small" wrap>
                      <Tag color={getStyleColor(item.style)}>
                        <Space size={2}>
                          <span>{getStyleIcon(item.style)}</span>
                          <span>{item.style}</span>
                        </Space>
                      </Tag>
                      <Tag color={qualityInfo.color}>
                        质量: {qualityInfo.label}
                      </Tag>
                      {item.isFavorite && (
                        <Tag color="gold" icon={<StarFilled />}>
                          收藏
                        </Tag>
                      )}
                    </Space>
                  </div>
                }
                description={
                  <div className="history-item-description">
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <Space size="small" wrap>
                        <Space size={2}>
                          <ClockCircleOutlined />
                          <Text type="secondary" style={{ fontSize: '11px' }}>
                            {formatTimestamp(item.timestamp)}
                          </Text>
                        </Space>
                        <Space size={2}>
                          <SettingOutlined />
                          <Text type="secondary" style={{ fontSize: '11px' }}>
                            对象数: {item.maxObjects}
                          </Text>
                        </Space>
                        {item.metadata && (
                          <>
                            <Text type="secondary" style={{ fontSize: '11px' }}>
                              实体: {item.metadata.objectCount}
                            </Text>
                            <Text type="secondary" style={{ fontSize: '11px' }}>
                              耗时: {formatGenerationTime(item.metadata.generationTime)}
                            </Text>
                          </>
                        )}
                      </Space>
                      
                      {item.understanding && (
                        <div className="understanding-info">
                          <Space size={2} wrap>
                            <Text type="secondary" style={{ fontSize: '10px' }}>
                              识别:
                            </Text>
                            {item.understanding.entities?.slice(0, 3).map((entity: any, index: number) => (
                              <Tag key={index} size="small" color="geekblue">
                                {entity.text}
                              </Tag>
                            ))}
                            {item.understanding.entities?.length > 3 && (
                              <Text type="secondary" style={{ fontSize: '10px' }}>
                                +{item.understanding.entities.length - 3}
                              </Text>
                            )}
                          </Space>
                        </div>
                      )}
                    </Space>
                  </div>
                }
              />
            </List.Item>
          );
        }}
      />
      
      {history.length > maxItems && (
        <div className="history-more">
          <Text type="secondary" style={{ fontSize: '12px' }}>
            显示 {maxItems} / {history.length} 条记录
          </Text>
        </div>
      )}
    </div>
  );
};

export default GenerationHistory;
