/**
 * NLP场景生成数据传输对象
 */
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsObject, Min, Max } from 'class-validator';

export class GenerateSceneDto {
  @ApiProperty({ description: '场景描述文本' })
  @IsString()
  text: string;

  @ApiProperty({ description: '生成风格', enum: ['realistic', 'cartoon', 'minimalist', 'scifi', 'fantasy'] })
  @IsString()
  style: string;

  @ApiProperty({ description: '质量等级', minimum: 1, maximum: 100 })
  @IsNumber()
  @Min(1)
  @Max(100)
  quality: number;

  @ApiProperty({ description: '最大对象数', minimum: 1, maximum: 100 })
  @IsNumber()
  @Min(1)
  @Max(100)
  maxObjects: number;

  @ApiProperty({ description: '用户ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: '项目ID', required: false })
  @IsOptional()
  @IsString()
  projectId?: string;

  @ApiProperty({ description: '额外约束条件', required: false })
  @IsOptional()
  @IsObject()
  constraints?: {
    maxPolygons?: number;
    targetFrameRate?: number;
  };
}

export class PreviewSceneDto {
  @ApiProperty({ description: '场景描述文本' })
  @IsString()
  text: string;

  @ApiProperty({ description: '生成风格' })
  @IsString()
  style: string;

  @ApiProperty({ description: '是否低质量预览', required: false })
  @IsOptional()
  lowQuality?: boolean;
}

export class SaveSceneDto {
  @ApiProperty({ description: '场景数据' })
  @IsObject()
  sceneData: any;

  @ApiProperty({ description: '场景名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '场景描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '用户ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: '项目ID', required: false })
  @IsOptional()
  @IsString()
  projectId?: string;

  @ApiProperty({ description: '标签', required: false })
  @IsOptional()
  tags?: string[];
}

export class GenerateSceneResponse {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '场景ID' })
  sceneId: string;

  @ApiProperty({ description: '场景URL' })
  sceneUrl?: string;

  @ApiProperty({ description: '场景数据' })
  sceneData: any;

  @ApiProperty({ description: '语言理解结果' })
  understanding: any;

  @ApiProperty({ description: '元数据' })
  metadata: {
    generationTime: number;
    objectCount: number;
    polygonCount: number;
  };
}
