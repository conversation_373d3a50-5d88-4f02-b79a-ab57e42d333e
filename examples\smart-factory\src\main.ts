/**
 * 智慧工厂应用启动脚本
 * 演示如何使用DL引擎构建智慧工厂系统
 */

import { SmartFactoryApplication } from './SmartFactoryApplication';

/**
 * 主函数
 */
async function main() {
  console.log('='.repeat(60));
  console.log('🏭 DL引擎智慧工厂系统演示');
  console.log('='.repeat(60));
  
  // 创建智慧工厂应用实例
  const factoryApp = new SmartFactoryApplication();
  
  try {
    // 启动应用
    await factoryApp.start();
    
    // 显示工厂状态
    console.log('\n📊 工厂状态信息:');
    console.log('-'.repeat(40));
    const status = factoryApp.getFactoryStatus();
    console.log(`工厂名称: ${status.factoryConfig.name}`);
    console.log(`设备数量: ${status.deviceCount}`);
    console.log(`连接状态: ${status.connections.length}/${status.deviceCount} 已连接`);
    console.log(`启动时间: ${status.timestamp.toLocaleString()}`);
    
    // 显示设备列表
    console.log('\n🔧 设备列表:');
    console.log('-'.repeat(40));
    status.factoryConfig.devices.forEach((device: any, index: number) => {
      console.log(`${index + 1}. ${device.name}`);
      console.log(`   类型: ${device.type}`);
      console.log(`   协议: ${device.protocol}`);
      console.log(`   地址: ${device.address}:${device.port}`);
      console.log(`   标签数: ${device.tags.length}`);
      console.log('');
    });
    
    // 显示生产线信息
    console.log('🏭 生产线信息:');
    console.log('-'.repeat(40));
    status.factoryConfig.productionLines.forEach((line: any, index: number) => {
      console.log(`${index + 1}. ${line.name}`);
      console.log(`   设备: ${line.devices.join(', ')}`);
      console.log(`   工艺步骤: ${line.workflow.length} 步`);
      console.log(`   产能: ${line.capacity} 件/天`);
      console.log(`   效率: ${line.efficiency}%`);
      console.log('');
    });
    
    // 模拟运行一段时间
    console.log('⏱️  系统运行中，按 Ctrl+C 停止...');
    console.log('-'.repeat(40));
    
    // 定期显示运行状态
    const statusInterval = setInterval(() => {
      const currentTime = new Date().toLocaleTimeString();
      console.log(`[${currentTime}] 系统正常运行中...`);
    }, 10000);
    
    // 监听退出信号
    process.on('SIGINT', async () => {
      console.log('\n\n🛑 收到停止信号，正在关闭系统...');
      clearInterval(statusInterval);
      
      try {
        await factoryApp.stop();
        console.log('✅ 系统已安全关闭');
        process.exit(0);
      } catch (error) {
        console.error('❌ 关闭系统时发生错误:', error);
        process.exit(1);
      }
    });
    
    // 演示一些操作
    setTimeout(() => {
      console.log('\n🔄 演示设备操作...');
      demonstrateDeviceOperations(factoryApp);
    }, 5000);
    
  } catch (error) {
    console.error('❌ 启动智慧工厂应用失败:', error);
    process.exit(1);
  }
}

/**
 * 演示设备操作
 */
async function demonstrateDeviceOperations(factoryApp: SmartFactoryApplication) {
  console.log('-'.repeat(40));
  
  try {
    // 获取当前状态
    const status = factoryApp.getFactoryStatus();
    
    // 模拟一些设备操作
    console.log('📡 模拟设备数据采集...');
    
    // 这里可以添加更多的演示操作
    // 例如：读取设备数据、发送控制命令、触发报警等
    
    setTimeout(() => {
      console.log('📈 生产数据分析...');
      
      // 模拟生产数据
      const productionData = {
        timestamp: new Date(),
        totalProduction: Math.floor(Math.random() * 100) + 50,
        qualityRate: (95 + Math.random() * 5).toFixed(1),
        efficiency: (80 + Math.random() * 15).toFixed(1),
        downtime: Math.floor(Math.random() * 30),
        energyConsumption: (150 + Math.random() * 50).toFixed(1)
      };
      
      console.log(`   总产量: ${productionData.totalProduction} 件`);
      console.log(`   合格率: ${productionData.qualityRate}%`);
      console.log(`   设备效率: ${productionData.efficiency}%`);
      console.log(`   停机时间: ${productionData.downtime} 分钟`);
      console.log(`   能耗: ${productionData.energyConsumption} kWh`);
      
    }, 3000);
    
    setTimeout(() => {
      console.log('⚠️  模拟报警处理...');
      
      // 模拟报警
      const alarms = [
        { level: 'WARNING', message: 'CNC机床主轴温度偏高', device: 'cnc-machine-001' },
        { level: 'INFO', message: '机械臂完成工件装夹', device: 'robot-arm-001' },
        { level: 'ALARM', message: '车间湿度超出正常范围', device: 'temperature-sensor-001' }
      ];
      
      alarms.forEach((alarm, index) => {
        setTimeout(() => {
          const icon = alarm.level === 'ALARM' ? '🚨' : alarm.level === 'WARNING' ? '⚠️' : 'ℹ️';
          console.log(`   ${icon} [${alarm.level}] ${alarm.message} (${alarm.device})`);
        }, index * 1000);
      });
      
    }, 6000);
    
  } catch (error) {
    console.error('演示操作失败:', error);
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
使用方法:
  npm start              启动智慧工厂演示
  npm run build          构建项目
  npm run test           运行测试

功能特性:
  ✅ 多种工业通信协议支持 (Modbus, OPC UA, MQTT)
  ✅ 设备数字孪生和3D可视化
  ✅ 实时数据采集和监控
  ✅ 生产线管理和调度
  ✅ 报警和异常处理
  ✅ 性能分析和OEE计算
  ✅ 可视化脚本编程

支持的设备类型:
  🔧 CNC机床
  🤖 工业机器人
  📡 各类传感器
  🏭 PLC控制器
  📦 传送带系统

更多信息请访问: https://github.com/your-repo/dl-engine
`);
}

// 检查命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// 启动应用
main().catch(error => {
  console.error('应用启动失败:', error);
  process.exit(1);
});
