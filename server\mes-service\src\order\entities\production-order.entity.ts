import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 生产订单状态枚举
 */
export enum OrderStatus {
  CREATED = 'created',
  PLANNED = 'planned',
  RELEASED = 'released',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold'
}

/**
 * 订单优先级枚举
 */
export enum OrderPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

/**
 * 生产订单实体
 */
@Entity('production_orders')
export class ProductionOrder {
  @ApiProperty({ description: '订单ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '订单编号' })
  @Column({ name: 'order_number', length: 50, unique: true })
  orderNumber: string;

  @ApiProperty({ description: '产品编码' })
  @Column({ name: 'product_code', length: 100 })
  productCode: string;

  @ApiProperty({ description: '产品名称' })
  @Column({ name: 'product_name', length: 200 })
  productName: string;

  @ApiProperty({ description: '产品规格' })
  @Column({ name: 'product_specification', type: 'text', nullable: true })
  productSpecification: string;

  @ApiProperty({ description: '计划数量' })
  @Column({ name: 'planned_quantity', type: 'int' })
  plannedQuantity: number;

  @ApiProperty({ description: '已完成数量' })
  @Column({ name: 'completed_quantity', type: 'int', default: 0 })
  completedQuantity: number;

  @ApiProperty({ description: '合格数量' })
  @Column({ name: 'qualified_quantity', type: 'int', default: 0 })
  qualifiedQuantity: number;

  @ApiProperty({ description: '不合格数量' })
  @Column({ name: 'defective_quantity', type: 'int', default: 0 })
  defectiveQuantity: number;

  @ApiProperty({ description: '订单状态', enum: OrderStatus })
  @Column({ type: 'enum', enum: OrderStatus, default: OrderStatus.CREATED })
  status: OrderStatus;

  @ApiProperty({ description: '优先级', enum: OrderPriority })
  @Column({ type: 'enum', enum: OrderPriority, default: OrderPriority.NORMAL })
  priority: OrderPriority;

  @ApiProperty({ description: '计划开始时间' })
  @Column({ name: 'planned_start_time', type: 'datetime', nullable: true })
  plannedStartTime: Date;

  @ApiProperty({ description: '计划完成时间' })
  @Column({ name: 'planned_end_time', type: 'datetime', nullable: true })
  plannedEndTime: Date;

  @ApiProperty({ description: '实际开始时间' })
  @Column({ name: 'actual_start_time', type: 'datetime', nullable: true })
  actualStartTime: Date;

  @ApiProperty({ description: '实际完成时间' })
  @Column({ name: 'actual_end_time', type: 'datetime', nullable: true })
  actualEndTime: Date;

  @ApiProperty({ description: '工艺路线ID' })
  @Column({ name: 'process_route_id', nullable: true })
  processRouteId: string;

  @ApiProperty({ description: '生产线ID' })
  @Column({ name: 'production_line_id', nullable: true })
  productionLineId: string;

  @ApiProperty({ description: '客户编码' })
  @Column({ name: 'customer_code', length: 100, nullable: true })
  customerCode: string;

  @ApiProperty({ description: '客户名称' })
  @Column({ name: 'customer_name', length: 200, nullable: true })
  customerName: string;

  @ApiProperty({ description: '交货日期' })
  @Column({ name: 'delivery_date', type: 'date', nullable: true })
  deliveryDate: Date;

  @ApiProperty({ description: '订单备注' })
  @Column({ type: 'text', nullable: true })
  remarks: string;

  @ApiProperty({ description: '订单配置' })
  @Column({ type: 'json', nullable: true })
  configuration: any;

  @ApiProperty({ description: '质量要求' })
  @Column({ name: 'quality_requirements', type: 'json', nullable: true })
  qualityRequirements: any;

  @ApiProperty({ description: '物料清单' })
  @Column({ name: 'bill_of_materials', type: 'json', nullable: true })
  billOfMaterials: any;

  @ApiProperty({ description: '成本预算' })
  @Column({ name: 'cost_budget', type: 'decimal', precision: 10, scale: 2, nullable: true })
  costBudget: number;

  @ApiProperty({ description: '实际成本' })
  @Column({ name: 'actual_cost', type: 'decimal', precision: 10, scale: 2, nullable: true })
  actualCost: number;

  @ApiProperty({ description: '创建人' })
  @Column({ name: 'created_by', length: 100 })
  createdBy: string;

  @ApiProperty({ description: '更新人' })
  @Column({ name: 'updated_by', length: 100, nullable: true })
  updatedBy: string;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 计算属性
  get completionRate(): number {
    return this.plannedQuantity > 0 ? (this.completedQuantity / this.plannedQuantity) * 100 : 0;
  }

  get qualityRate(): number {
    return this.completedQuantity > 0 ? (this.qualifiedQuantity / this.completedQuantity) * 100 : 0;
  }

  get isOverdue(): boolean {
    if (!this.plannedEndTime) return false;
    return new Date() > this.plannedEndTime && this.status !== OrderStatus.COMPLETED;
  }

  get remainingQuantity(): number {
    return Math.max(0, this.plannedQuantity - this.completedQuantity);
  }

  get estimatedDuration(): number {
    if (!this.plannedStartTime || !this.plannedEndTime) return 0;
    return this.plannedEndTime.getTime() - this.plannedStartTime.getTime();
  }

  get actualDuration(): number {
    if (!this.actualStartTime || !this.actualEndTime) return 0;
    return this.actualEndTime.getTime() - this.actualStartTime.getTime();
  }
}
