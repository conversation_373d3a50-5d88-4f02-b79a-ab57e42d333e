/**
 * NLP场景生成控制器
 */
import {
  Controller, Post, Get, Body, Param, Query,
  UseGuards, HttpStatus, HttpException
} from '@nestjs/common';
import {
  ApiTags, ApiOperation, ApiResponse,
  ApiBearerAuth, ApiBody, ApiParam
} from '@nestjs/swagger';
import { NLPSceneService } from './nlp-scene.service';
import {
  GenerateSceneDto, PreviewSceneDto,
  SaveSceneDto, GenerateSceneResponse
} from './dto/nlp-scene.dto';

@ApiTags('自然语言场景生成')
@Controller('api/v1/nlp-scene')
export class NLPSceneController {
  constructor(private readonly nlpSceneService: NLPSceneService) {}

  @Post('generate')
  @ApiOperation({ summary: '生成场景' })
  @ApiBody({ type: GenerateSceneDto })
  @ApiResponse({
    status: 200,
    description: '场景生成成功',
    type: GenerateSceneResponse
  })
  async generateScene(@Body() generateDto: GenerateSceneDto) {
    try {
      const result = await this.nlpSceneService.generateScene(generateDto);
      return {
        success: true,
        data: result,
        message: '场景生成成功'
      };
    } catch (error: any) {
      throw new HttpException(
        `场景生成失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post('preview')
  @ApiOperation({ summary: '预览场景' })
  @ApiBody({ type: PreviewSceneDto })
  async previewScene(@Body() previewDto: PreviewSceneDto) {
    try {
      const result = await this.nlpSceneService.previewScene(previewDto);
      return {
        success: true,
        data: result,
        message: '预览生成成功'
      };
    } catch (error: any) {
      throw new HttpException(
        `预览生成失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get('history/:userId')
  @ApiOperation({ summary: '获取用户生成历史' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  async getGenerationHistory(
    @Param('userId') userId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('style') style?: string
  ) {
    try {
      const result = await this.nlpSceneService.getGenerationHistory(
        userId,
        page,
        limit,
        style
      );
      return {
        success: true,
        data: result,
        message: '获取历史记录成功'
      };
    } catch (error: any) {
      throw new HttpException(
        `获取历史记录失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('save')
  @ApiOperation({ summary: '保存生成的场景' })
  @ApiBody({ type: SaveSceneDto })
  async saveGeneratedScene(@Body() saveDto: SaveSceneDto) {
    try {
      const result = await this.nlpSceneService.saveGeneratedScene(saveDto);
      return {
        success: true,
        data: result,
        message: '场景保存成功'
      };
    } catch (error: any) {
      throw new HttpException(
        `场景保存失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get('templates')
  @ApiOperation({ summary: '获取场景模板' })
  async getSceneTemplates(
    @Query('category') category?: string,
    @Query('style') style?: string
  ) {
    try {
      const result = await this.nlpSceneService.getSceneTemplates(category, style);
      return {
        success: true,
        data: result,
        message: '获取模板成功'
      };
    } catch (error: any) {
      throw new HttpException(
        `获取模板失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('stats/:userId')
  @ApiOperation({ summary: '获取用户生成统计' })
  async getUserStats(@Param('userId') userId: string) {
    try {
      const result = await this.nlpSceneService.getUserStats(userId);
      return {
        success: true,
        data: result,
        message: '获取统计信息成功'
      };
    } catch (error: any) {
      throw new HttpException(
        `获取统计信息失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
