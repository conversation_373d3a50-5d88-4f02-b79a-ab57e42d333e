import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In, Not } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ProductionOrder, OrderStatus, OrderPriority } from './entities/production-order.entity';
import { CreateOrderDto, UpdateOrderDto, OrderQueryDto } from './dto/order.dto';
import * as moment from 'moment';

/**
 * 订单统计接口
 */
interface OrderStatistics {
  totalOrders: number;
  ordersByStatus: Record<OrderStatus, number>;
  ordersByPriority: Record<OrderPriority, number>;
  completionRate: number;
  qualityRate: number;
  overdueOrders: number;
  avgCycleTime: number;
}

/**
 * 生产订单服务
 */
@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);

  constructor(
    @InjectRepository(ProductionOrder)
    private orderRepository: Repository<ProductionOrder>,
  ) {}

  /**
   * 创建生产订单
   * @param createOrderDto 创建订单DTO
   * @returns 创建的订单
   */
  async createOrder(createOrderDto: CreateOrderDto): Promise<ProductionOrder> {
    try {
      // 生成订单编号
      const orderNumber = await this.generateOrderNumber();
      
      const order = this.orderRepository.create({
        ...createOrderDto,
        orderNumber,
        status: OrderStatus.CREATED,
      });

      const savedOrder = await this.orderRepository.save(order);
      
      this.logger.log(`生产订单创建成功: ${savedOrder.orderNumber}`);
      return savedOrder;
      
    } catch (error) {
      this.logger.error('创建生产订单失败', error);
      throw new BadRequestException('创建生产订单失败');
    }
  }

  /**
   * 更新生产订单
   * @param id 订单ID
   * @param updateOrderDto 更新订单DTO
   * @returns 更新后的订单
   */
  async updateOrder(id: string, updateOrderDto: UpdateOrderDto): Promise<ProductionOrder> {
    const order = await this.findOrderById(id);
    
    // 检查状态变更的合法性
    if (updateOrderDto.status && !this.isValidStatusTransition(order.status, updateOrderDto.status)) {
      throw new BadRequestException(`无效的状态变更: ${order.status} -> ${updateOrderDto.status}`);
    }

    // 更新订单
    Object.assign(order, updateOrderDto);
    
    // 自动设置时间戳
    if (updateOrderDto.status === OrderStatus.IN_PROGRESS && !order.actualStartTime) {
      order.actualStartTime = new Date();
    }
    
    if (updateOrderDto.status === OrderStatus.COMPLETED && !order.actualEndTime) {
      order.actualEndTime = new Date();
    }

    const updatedOrder = await this.orderRepository.save(order);
    
    this.logger.log(`生产订单更新成功: ${updatedOrder.orderNumber}`);
    return updatedOrder;
  }

  /**
   * 根据ID查找订单
   * @param id 订单ID
   * @returns 订单实体
   */
  async findOrderById(id: string): Promise<ProductionOrder> {
    const order = await this.orderRepository.findOne({ where: { id } });
    
    if (!order) {
      throw new NotFoundException(`订单不存在: ${id}`);
    }
    
    return order;
  }

  /**
   * 根据订单编号查找订单
   * @param orderNumber 订单编号
   * @returns 订单实体
   */
  async findOrderByNumber(orderNumber: string): Promise<ProductionOrder> {
    const order = await this.orderRepository.findOne({ where: { orderNumber } });
    
    if (!order) {
      throw new NotFoundException(`订单不存在: ${orderNumber}`);
    }
    
    return order;
  }

  /**
   * 查询订单列表
   * @param queryDto 查询条件
   * @returns 订单列表和总数
   */
  async findOrders(queryDto: OrderQueryDto): Promise<{ orders: ProductionOrder[]; total: number }> {
    const {
      page = 1,
      limit = 20,
      status,
      priority,
      productCode,
      customerCode,
      startDate,
      endDate,
      overdue,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = queryDto;

    const queryBuilder = this.orderRepository.createQueryBuilder('order');

    // 状态筛选
    if (status) {
      if (Array.isArray(status)) {
        queryBuilder.andWhere('order.status IN (:...status)', { status });
      } else {
        queryBuilder.andWhere('order.status = :status', { status });
      }
    }

    // 优先级筛选
    if (priority) {
      if (Array.isArray(priority)) {
        queryBuilder.andWhere('order.priority IN (:...priority)', { priority });
      } else {
        queryBuilder.andWhere('order.priority = :priority', { priority });
      }
    }

    // 产品编码筛选
    if (productCode) {
      queryBuilder.andWhere('order.productCode LIKE :productCode', { 
        productCode: `%${productCode}%` 
      });
    }

    // 客户编码筛选
    if (customerCode) {
      queryBuilder.andWhere('order.customerCode LIKE :customerCode', { 
        customerCode: `%${customerCode}%` 
      });
    }

    // 日期范围筛选
    if (startDate && endDate) {
      queryBuilder.andWhere('order.createdAt BETWEEN :startDate AND :endDate', {
        startDate: moment(startDate).startOf('day').toDate(),
        endDate: moment(endDate).endOf('day').toDate()
      });
    }

    // 逾期订单筛选
    if (overdue) {
      queryBuilder.andWhere('order.plannedEndTime < :now', { now: new Date() });
      queryBuilder.andWhere('order.status != :completedStatus', { 
        completedStatus: OrderStatus.COMPLETED 
      });
    }

    // 排序
    queryBuilder.orderBy(`order.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // 分页
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [orders, total] = await queryBuilder.getManyAndCount();

    return { orders, total };
  }

  /**
   * 获取订单统计信息
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @returns 统计信息
   */
  async getOrderStatistics(startDate?: Date, endDate?: Date): Promise<OrderStatistics> {
    const queryBuilder = this.orderRepository.createQueryBuilder('order');

    // 日期范围筛选
    if (startDate && endDate) {
      queryBuilder.where('order.createdAt BETWEEN :startDate AND :endDate', {
        startDate: moment(startDate).startOf('day').toDate(),
        endDate: moment(endDate).endOf('day').toDate()
      });
    }

    const orders = await queryBuilder.getMany();

    // 初始化统计数据
    const statistics: OrderStatistics = {
      totalOrders: orders.length,
      ordersByStatus: {} as Record<OrderStatus, number>,
      ordersByPriority: {} as Record<OrderPriority, number>,
      completionRate: 0,
      qualityRate: 0,
      overdueOrders: 0,
      avgCycleTime: 0
    };

    // 初始化计数器
    Object.values(OrderStatus).forEach(status => {
      statistics.ordersByStatus[status] = 0;
    });

    Object.values(OrderPriority).forEach(priority => {
      statistics.ordersByPriority[priority] = 0;
    });

    // 统计数据
    let totalPlannedQuantity = 0;
    let totalCompletedQuantity = 0;
    let totalQualifiedQuantity = 0;
    let totalCycleTime = 0;
    let completedOrdersCount = 0;

    orders.forEach(order => {
      // 按状态统计
      statistics.ordersByStatus[order.status]++;
      
      // 按优先级统计
      statistics.ordersByPriority[order.priority]++;
      
      // 数量统计
      totalPlannedQuantity += order.plannedQuantity;
      totalCompletedQuantity += order.completedQuantity;
      totalQualifiedQuantity += order.qualifiedQuantity;
      
      // 逾期订单统计
      if (order.isOverdue) {
        statistics.overdueOrders++;
      }
      
      // 周期时间统计
      if (order.status === OrderStatus.COMPLETED && order.actualDuration > 0) {
        totalCycleTime += order.actualDuration;
        completedOrdersCount++;
      }
    });

    // 计算比率
    statistics.completionRate = totalPlannedQuantity > 0 ? 
      (totalCompletedQuantity / totalPlannedQuantity) * 100 : 0;
    
    statistics.qualityRate = totalCompletedQuantity > 0 ? 
      (totalQualifiedQuantity / totalCompletedQuantity) * 100 : 0;
    
    statistics.avgCycleTime = completedOrdersCount > 0 ? 
      totalCycleTime / completedOrdersCount : 0;

    return statistics;
  }

  /**
   * 更新订单进度
   * @param id 订单ID
   * @param completedQuantity 完成数量
   * @param qualifiedQuantity 合格数量
   * @param defectiveQuantity 不合格数量
   */
  async updateOrderProgress(
    id: string, 
    completedQuantity: number, 
    qualifiedQuantity: number, 
    defectiveQuantity: number
  ): Promise<ProductionOrder> {
    const order = await this.findOrderById(id);
    
    // 验证数量的合理性
    if (completedQuantity < 0 || qualifiedQuantity < 0 || defectiveQuantity < 0) {
      throw new BadRequestException('数量不能为负数');
    }
    
    if (qualifiedQuantity + defectiveQuantity !== completedQuantity) {
      throw new BadRequestException('合格数量 + 不合格数量必须等于完成数量');
    }
    
    if (completedQuantity > order.plannedQuantity) {
      throw new BadRequestException('完成数量不能超过计划数量');
    }

    // 更新数量
    order.completedQuantity = completedQuantity;
    order.qualifiedQuantity = qualifiedQuantity;
    order.defectiveQuantity = defectiveQuantity;

    // 自动更新状态
    if (completedQuantity === 0 && order.status === OrderStatus.IN_PROGRESS) {
      // 保持进行中状态
    } else if (completedQuantity > 0 && order.status === OrderStatus.RELEASED) {
      order.status = OrderStatus.IN_PROGRESS;
      order.actualStartTime = new Date();
    } else if (completedQuantity === order.plannedQuantity) {
      order.status = OrderStatus.COMPLETED;
      order.actualEndTime = new Date();
    }

    const updatedOrder = await this.orderRepository.save(order);
    
    this.logger.log(`订单进度更新: ${order.orderNumber} - 完成: ${completedQuantity}/${order.plannedQuantity}`);
    return updatedOrder;
  }

  /**
   * 释放订单到生产
   * @param id 订单ID
   */
  async releaseOrder(id: string): Promise<ProductionOrder> {
    const order = await this.findOrderById(id);
    
    if (order.status !== OrderStatus.PLANNED) {
      throw new BadRequestException('只有已计划的订单才能释放到生产');
    }

    order.status = OrderStatus.RELEASED;
    
    const updatedOrder = await this.orderRepository.save(order);
    
    this.logger.log(`订单释放到生产: ${order.orderNumber}`);
    return updatedOrder;
  }

  /**
   * 取消订单
   * @param id 订单ID
   * @param reason 取消原因
   */
  async cancelOrder(id: string, reason?: string): Promise<ProductionOrder> {
    const order = await this.findOrderById(id);
    
    if (order.status === OrderStatus.COMPLETED) {
      throw new BadRequestException('已完成的订单不能取消');
    }

    order.status = OrderStatus.CANCELLED;
    if (reason) {
      order.remarks = (order.remarks || '') + `\n取消原因: ${reason}`;
    }
    
    const updatedOrder = await this.orderRepository.save(order);
    
    this.logger.log(`订单已取消: ${order.orderNumber}`);
    return updatedOrder;
  }

  /**
   * 生成订单编号
   */
  private async generateOrderNumber(): Promise<string> {
    const today = moment().format('YYYYMMDD');
    const prefix = `PO${today}`;
    
    // 查找今天的最大序号
    const lastOrder = await this.orderRepository
      .createQueryBuilder('order')
      .where('order.orderNumber LIKE :prefix', { prefix: `${prefix}%` })
      .orderBy('order.orderNumber', 'DESC')
      .getOne();

    let sequence = 1;
    if (lastOrder) {
      const lastSequence = parseInt(lastOrder.orderNumber.substring(prefix.length));
      sequence = lastSequence + 1;
    }

    return `${prefix}${sequence.toString().padStart(4, '0')}`;
  }

  /**
   * 验证状态变更的合法性
   * @param currentStatus 当前状态
   * @param newStatus 新状态
   */
  private isValidStatusTransition(currentStatus: OrderStatus, newStatus: OrderStatus): boolean {
    const validTransitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.CREATED]: [OrderStatus.PLANNED, OrderStatus.CANCELLED],
      [OrderStatus.PLANNED]: [OrderStatus.RELEASED, OrderStatus.ON_HOLD, OrderStatus.CANCELLED],
      [OrderStatus.RELEASED]: [OrderStatus.IN_PROGRESS, OrderStatus.ON_HOLD, OrderStatus.CANCELLED],
      [OrderStatus.IN_PROGRESS]: [OrderStatus.COMPLETED, OrderStatus.ON_HOLD, OrderStatus.CANCELLED],
      [OrderStatus.ON_HOLD]: [OrderStatus.RELEASED, OrderStatus.CANCELLED],
      [OrderStatus.COMPLETED]: [], // 已完成的订单不能变更状态
      [OrderStatus.CANCELLED]: [] // 已取消的订单不能变更状态
    };

    return validTransitions[currentStatus]?.includes(newStatus) || false;
  }

  /**
   * 定时检查逾期订单
   */
  @Cron(CronExpression.EVERY_HOUR)
  async checkOverdueOrders(): Promise<void> {
    try {
      const overdueOrders = await this.orderRepository.find({
        where: {
          plannedEndTime: Between(new Date(0), new Date()),
          status: Not(In([OrderStatus.COMPLETED, OrderStatus.CANCELLED]))
        }
      });

      if (overdueOrders.length > 0) {
        this.logger.warn(`发现 ${overdueOrders.length} 个逾期订单`);
        
        // 这里可以发送通知或执行其他逾期处理逻辑
        overdueOrders.forEach(order => {
          this.logger.warn(`逾期订单: ${order.orderNumber} - 计划完成时间: ${order.plannedEndTime}`);
        });
      }
      
    } catch (error) {
      this.logger.error('检查逾期订单失败', error);
    }
  }

  /**
   * 删除订单
   * @param id 订单ID
   */
  async deleteOrder(id: string): Promise<void> {
    const order = await this.findOrderById(id);
    
    if (order.status === OrderStatus.IN_PROGRESS) {
      throw new BadRequestException('进行中的订单不能删除');
    }

    await this.orderRepository.remove(order);
    
    this.logger.log(`订单已删除: ${order.orderNumber}`);
  }
}
