/**
 * LearningTrackingNodes.ts
 * 
 * 学习跟踪节点 - 提供xAPI协议、学习记录、进度分析、个性化推荐等功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * xAPI学习记录节点
 */
export class XAPILearningRecordNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'learning/xapi/record',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('actor', SocketType.OBJECT, {}, '学习者信息');
    this.addInputSocket('verb', SocketType.STRING, 'experienced', '动作类型');
    this.addInputSocket('object', SocketType.OBJECT, {}, '学习对象');
    this.addInputSocket('result', SocketType.OBJECT, {}, '学习结果');
    this.addInputSocket('context', SocketType.OBJECT, {}, '学习上下文');
    
    // 输出插槽
    this.addOutputSocket('statementId', SocketType.STRING, '', '语句ID');
    this.addOutputSocket('timestamp', SocketType.STRING, '', '时间戳');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '记录成功');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const actor = this.getInputValue('actor') as any;
    const verb = this.getInputValue('verb') as string;
    const object = this.getInputValue('object') as any;
    const result = this.getInputValue('result') as any;
    const context = this.getInputValue('context') as any;

    if (!actor || !verb || !object) {
      this.setOutputValue('success', false);
      return { success: false, error: '学习者、动作和对象信息不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const xapiService = world.getSystem('XAPIService');
      
      if (!xapiService) {
        this.setOutputValue('success', false);
        return { success: false, error: 'xAPI服务不可用' };
      }

      // 创建xAPI语句
      const statement = {
        actor,
        verb: {
          id: `http://adlnet.gov/expapi/verbs/${verb}`,
          display: { 'en-US': verb, 'zh-CN': this.getVerbDisplayName(verb) }
        },
        object,
        result,
        context,
        timestamp: new Date().toISOString()
      };

      // 发送xAPI语句
      const response = await xapiService.sendStatement(statement);

      this.setOutputValue('success', response.success);
      this.setOutputValue('statementId', response.statementId || '');
      this.setOutputValue('timestamp', statement.timestamp);
      
      return response;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }

  private getVerbDisplayName(verb: string): string {
    const verbMap: { [key: string]: string } = {
      'experienced': '体验了',
      'completed': '完成了',
      'passed': '通过了',
      'failed': '失败了',
      'answered': '回答了',
      'interacted': '交互了',
      'attempted': '尝试了',
      'mastered': '掌握了'
    };
    return verbMap[verb] || verb;
  }
}

/**
 * 学习进度分析节点
 */
export class LearningProgressAnalysisNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'learning/analysis/progress',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('learnerId', SocketType.STRING, '', '学习者ID');
    this.addInputSocket('courseId', SocketType.STRING, '', '课程ID');
    this.addInputSocket('timeRange', SocketType.OBJECT, {}, '时间范围');
    this.addInputSocket('analysisType', SocketType.STRING, 'comprehensive', '分析类型');
    
    // 输出插槽
    this.addOutputSocket('progressData', SocketType.OBJECT, {}, '进度数据');
    this.addOutputSocket('completionRate', SocketType.NUMBER, 0, '完成率');
    this.addOutputSocket('timeSpent', SocketType.NUMBER, 0, '学习时长');
    this.addOutputSocket('performance', SocketType.OBJECT, {}, '学习表现');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '分析成功');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const learnerId = this.getInputValue('learnerId') as string;
    const courseId = this.getInputValue('courseId') as string;
    const timeRange = this.getInputValue('timeRange') as any;
    const analysisType = this.getInputValue('analysisType') as string;

    if (!learnerId) {
      this.setOutputValue('success', false);
      return { success: false, error: '学习者ID不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const analyticsService = world.getSystem('LearningAnalyticsService');
      
      if (!analyticsService) {
        this.setOutputValue('success', false);
        return { success: false, error: '学习分析服务不可用' };
      }

      // 执行进度分析
      const analysis = await analyticsService.analyzeProgress({
        learnerId,
        courseId,
        timeRange,
        analysisType
      });

      this.setOutputValue('success', analysis.success);
      this.setOutputValue('progressData', analysis.progressData || {});
      this.setOutputValue('completionRate', analysis.completionRate || 0);
      this.setOutputValue('timeSpent', analysis.timeSpent || 0);
      this.setOutputValue('performance', analysis.performance || {});
      
      return analysis;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 个性化推荐节点
 */
export class PersonalizedRecommendationNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'learning/recommendation/personalized',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('learnerId', SocketType.STRING, '', '学习者ID');
    this.addInputSocket('currentContext', SocketType.OBJECT, {}, '当前上下文');
    this.addInputSocket('preferences', SocketType.OBJECT, {}, '学习偏好');
    this.addInputSocket('maxRecommendations', SocketType.NUMBER, 5, '最大推荐数');
    
    // 输出插槽
    this.addOutputSocket('recommendations', SocketType.ARRAY, [], '推荐内容');
    this.addOutputSocket('confidence', SocketType.NUMBER, 0, '推荐置信度');
    this.addOutputSocket('reasoning', SocketType.ARRAY, [], '推荐理由');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '推荐成功');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const learnerId = this.getInputValue('learnerId') as string;
    const currentContext = this.getInputValue('currentContext') as any;
    const preferences = this.getInputValue('preferences') as any;
    const maxRecommendations = this.getInputValue('maxRecommendations') as number;

    if (!learnerId) {
      this.setOutputValue('success', false);
      return { success: false, error: '学习者ID不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const recommendationService = world.getSystem('RecommendationService');
      
      if (!recommendationService) {
        this.setOutputValue('success', false);
        return { success: false, error: '推荐服务不可用' };
      }

      // 生成个性化推荐
      const recommendations = await recommendationService.generateRecommendations({
        learnerId,
        currentContext,
        preferences,
        maxRecommendations
      });

      this.setOutputValue('success', recommendations.success);
      this.setOutputValue('recommendations', recommendations.items || []);
      this.setOutputValue('confidence', recommendations.confidence || 0);
      this.setOutputValue('reasoning', recommendations.reasoning || []);
      
      return recommendations;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 学习路径规划节点
 */
export class LearningPathPlanningNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'learning/planning/path',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('learnerId', SocketType.STRING, '', '学习者ID');
    this.addInputSocket('targetSkills', SocketType.ARRAY, [], '目标技能');
    this.addInputSocket('currentLevel', SocketType.OBJECT, {}, '当前水平');
    this.addInputSocket('timeConstraints', SocketType.OBJECT, {}, '时间约束');
    this.addInputSocket('learningStyle', SocketType.STRING, 'adaptive', '学习风格');
    
    // 输出插槽
    this.addOutputSocket('learningPath', SocketType.ARRAY, [], '学习路径');
    this.addOutputSocket('estimatedDuration', SocketType.NUMBER, 0, '预计时长');
    this.addOutputSocket('milestones', SocketType.ARRAY, [], '里程碑');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '规划成功');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const learnerId = this.getInputValue('learnerId') as string;
    const targetSkills = this.getInputValue('targetSkills') as string[];
    const currentLevel = this.getInputValue('currentLevel') as any;
    const timeConstraints = this.getInputValue('timeConstraints') as any;
    const learningStyle = this.getInputValue('learningStyle') as string;

    if (!learnerId || !targetSkills || targetSkills.length === 0) {
      this.setOutputValue('success', false);
      return { success: false, error: '学习者ID和目标技能不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const planningService = world.getSystem('LearningPlanningService');
      
      if (!planningService) {
        this.setOutputValue('success', false);
        return { success: false, error: '学习规划服务不可用' };
      }

      // 生成学习路径
      const pathPlan = await planningService.generateLearningPath({
        learnerId,
        targetSkills,
        currentLevel,
        timeConstraints,
        learningStyle
      });

      this.setOutputValue('success', pathPlan.success);
      this.setOutputValue('learningPath', pathPlan.path || []);
      this.setOutputValue('estimatedDuration', pathPlan.estimatedDuration || 0);
      this.setOutputValue('milestones', pathPlan.milestones || []);
      
      return pathPlan;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册学习跟踪节点
 */
export function registerLearningTrackingNodes(registry: NodeRegistry): void {
  // xAPI学习记录节点
  registry.registerNodeType({
    type: 'learning/xapi/record',
    category: NodeCategory.CUSTOM,
    constructor: XAPILearningRecordNode,
    label: 'xAPI学习记录',
    description: '记录xAPI格式的学习数据',
    tags: ['learning', 'xapi', 'record'],
    version: '1.0.0'
  });

  // 学习进度分析节点
  registry.registerNodeType({
    type: 'learning/analysis/progress',
    category: NodeCategory.CUSTOM,
    constructor: LearningProgressAnalysisNode,
    label: '学习进度分析',
    description: '分析学习者的学习进度',
    tags: ['learning', 'analysis', 'progress'],
    version: '1.0.0'
  });

  // 个性化推荐节点
  registry.registerNodeType({
    type: 'learning/recommendation/personalized',
    category: NodeCategory.CUSTOM,
    constructor: PersonalizedRecommendationNode,
    label: '个性化推荐',
    description: '生成个性化学习推荐',
    tags: ['learning', 'recommendation', 'personalized'],
    version: '1.0.0'
  });

  // 学习路径规划节点
  registry.registerNodeType({
    type: 'learning/planning/path',
    category: NodeCategory.CUSTOM,
    constructor: LearningPathPlanningNode,
    label: '学习路径规划',
    description: '规划个性化学习路径',
    tags: ['learning', 'planning', 'path'],
    version: '1.0.0'
  });
}
