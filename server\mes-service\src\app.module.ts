import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { OrderModule } from './order/order.module';
import { ProcessModule } from './process/process.module';
import { QualityModule } from './quality/quality.module';
import { InventoryModule } from './inventory/inventory.module';
import { SchedulingModule } from './scheduling/scheduling.module';
import { TrackingModule } from './tracking/tracking.module';
import { ReportModule } from './report/report.module';
import { WebSocketModule } from './websocket/websocket.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', ''),
        database: configService.get('DB_DATABASE', 'mes_system'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') === 'development',
        timezone: '+08:00',
      }),
      inject: [ConfigService],
    }),
    
    // 定时任务模块
    ScheduleModule.forRoot(),
    
    // 业务模块
    OrderModule,
    ProcessModule,
    QualityModule,
    InventoryModule,
    SchedulingModule,
    TrackingModule,
    ReportModule,
    WebSocketModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
