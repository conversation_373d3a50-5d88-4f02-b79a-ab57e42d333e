{"name": "enterprise-integration-service", "version": "1.0.0", "description": "企业系统深度集成服务 - ERP/CRM/SCM/财务/人力资源系统无缝集成", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "start:dev": "ts-node src/main.ts", "start:debug": "ts-node --inspect src/main.ts", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/websockets": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/config": "^10.0.0", "@nestjs/schedule": "^3.0.0", "@nestjs/swagger": "^7.1.8", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "redis": "^4.6.7", "socket.io": "^4.7.2", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "rxjs": "^7.8.1", "reflect-metadata": "^0.1.13", "uuid": "^9.0.0", "moment": "^2.29.4", "lodash": "^4.17.21", "sap-rfc": "^2.0.0", "oracle-db": "^5.5.0", "mssql": "^9.1.1", "soap": "^0.45.0", "xml2js": "^0.6.2", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "apache-kafka": "^2.0.0", "rabbitmq": "^1.0.0", "enterprise-service-bus": "^1.0.0", "data-transformation": "^1.0.0", "workflow-engine": "^1.0.0", "business-rules-engine": "^1.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/uuid": "^9.0.2", "@types/lodash": "^4.14.195", "@types/xml2js": "^0.4.11", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}