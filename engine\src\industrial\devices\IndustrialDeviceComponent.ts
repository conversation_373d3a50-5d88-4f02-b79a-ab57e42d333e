import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';
import { Vector3 } from '../../math/Vector3';
import { 
  DeviceType, 
  DeviceStatus, 
  DeviceConfig, 
  IndustrialDataPoint, 
  DevicePerformance,
  AlarmConfig,
  AlarmSeverity 
} from '../types';
import { Debug } from '../../core/Debug';

/**
 * 工业设备基础组件
 * 所有工业设备的基类，提供通用的设备功能
 */
export abstract class IndustrialDeviceComponent extends Component {
  // 设备基本信息
  public deviceId: string = '';
  public deviceName: string = '';
  public abstract deviceType: DeviceType;
  public manufacturer: string = '';
  public model: string = '';
  public serialNumber: string = '';
  
  // 设备状态
  public status: DeviceStatus = DeviceStatus.OFFLINE;
  public lastStatusChange: Date = new Date();
  public errorMessage: string = '';
  public maintenanceMode: boolean = false;
  
  // 位置和方向
  public position: Vector3 = new Vector3();
  public rotation: Vector3 = new Vector3();
  public scale: Vector3 = new Vector3(1, 1, 1);
  
  // 性能指标
  public performance: DevicePerformance = {
    deviceId: '',
    timestamp: new Date(),
    availability: 0,
    performance: 0,
    quality: 0,
    oee: 0,
    mtbf: 0,
    mttr: 0
  };
  
  // 数据点
  protected dataPoints: Map<string, IndustrialDataPoint> = new Map();
  protected alarms: Map<string, AlarmConfig> = new Map();
  protected activeAlarms: Set<string> = new Set();
  
  // 事件回调
  public onStatusChanged?: (status: DeviceStatus, previousStatus: DeviceStatus) => void;
  public onDataUpdated?: (dataPoint: IndustrialDataPoint) => void;
  public onAlarmTriggered?: (alarmId: string, alarm: AlarmConfig) => void;
  public onAlarmCleared?: (alarmId: string) => void;
  
  // 配置选项
  protected options = {
    updateInterval: 1000,
    enablePerformanceTracking: true,
    enableAlarmProcessing: true,
    dataRetentionTime: 300000 // 5分钟
  };

  constructor(entity: Entity, config?: DeviceConfig) {
    super(entity);
    
    if (config) {
      this.initializeFromConfig(config);
    }
    
    this.deviceId = this.entity.id;
    this.performance.deviceId = this.deviceId;
    
    // 启动更新循环
    this.startUpdateLoop();
  }

  /**
   * 从配置初始化设备
   * @param config 设备配置
   */
  protected initializeFromConfig(config: DeviceConfig): void {
    this.deviceId = config.id;
    this.deviceName = config.name;
    this.manufacturer = config.parameters.manufacturer || '';
    this.model = config.parameters.model || '';
    this.serialNumber = config.parameters.serialNumber || '';
    
    // 初始化报警配置
    if (config.tags) {
      config.tags.forEach(tag => {
        if (tag.alarms) {
          tag.alarms.forEach(alarm => {
            this.alarms.set(alarm.id, alarm);
          });
        }
      });
    }
  }

  /**
   * 更新设备状态
   * @param newStatus 新状态
   */
  public updateStatus(newStatus: DeviceStatus): void {
    const previousStatus = this.status;
    if (previousStatus !== newStatus) {
      this.status = newStatus;
      this.lastStatusChange = new Date();
      
      Debug.log('IndustrialDevice', `设备状态变更: ${this.deviceName} ${previousStatus} -> ${newStatus}`);
      
      if (this.onStatusChanged) {
        this.onStatusChanged(newStatus, previousStatus);
      }
      
      // 更新性能指标
      this.updatePerformanceMetrics();
    }
  }

  /**
   * 更新数据点
   * @param dataPoint 数据点
   */
  public updateDataPoint(dataPoint: IndustrialDataPoint): void {
    this.dataPoints.set(dataPoint.tagId, dataPoint);
    
    if (this.onDataUpdated) {
      this.onDataUpdated(dataPoint);
    }
    
    // 检查报警条件
    if (this.options.enableAlarmProcessing) {
      this.checkAlarms(dataPoint);
    }
  }

  /**
   * 获取数据点值
   * @param tagId 标签ID
   * @returns 数据点值
   */
  public getDataPointValue(tagId: string): any {
    const dataPoint = this.dataPoints.get(tagId);
    return dataPoint ? dataPoint.value : null;
  }

  /**
   * 获取所有数据点
   * @returns 数据点数组
   */
  public getAllDataPoints(): IndustrialDataPoint[] {
    return Array.from(this.dataPoints.values());
  }

  /**
   * 设置维护模式
   * @param enabled 是否启用维护模式
   */
  public setMaintenanceMode(enabled: boolean): void {
    this.maintenanceMode = enabled;
    
    if (enabled) {
      this.updateStatus(DeviceStatus.MAINTENANCE);
    } else {
      this.updateStatus(DeviceStatus.ONLINE);
    }
    
    Debug.log('IndustrialDevice', `设备维护模式: ${this.deviceName} ${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 触发报警
   * @param alarmId 报警ID
   */
  protected triggerAlarm(alarmId: string): void {
    if (!this.activeAlarms.has(alarmId)) {
      this.activeAlarms.add(alarmId);
      const alarm = this.alarms.get(alarmId);
      
      if (alarm) {
        Debug.warn('IndustrialDevice', `设备报警触发: ${this.deviceName} - ${alarm.message}`);
        
        if (this.onAlarmTriggered) {
          this.onAlarmTriggered(alarmId, alarm);
        }
        
        // 根据报警严重程度更新设备状态
        if (alarm.severity === AlarmSeverity.CRITICAL) {
          this.updateStatus(DeviceStatus.ERROR);
        } else if (alarm.severity === AlarmSeverity.ALARM) {
          this.updateStatus(DeviceStatus.ALARM);
        }
      }
    }
  }

  /**
   * 清除报警
   * @param alarmId 报警ID
   */
  protected clearAlarm(alarmId: string): void {
    if (this.activeAlarms.has(alarmId)) {
      this.activeAlarms.delete(alarmId);
      
      Debug.log('IndustrialDevice', `设备报警清除: ${this.deviceName} - ${alarmId}`);
      
      if (this.onAlarmCleared) {
        this.onAlarmCleared(alarmId);
      }
      
      // 如果没有活动报警，恢复正常状态
      if (this.activeAlarms.size === 0 && this.status === DeviceStatus.ALARM) {
        this.updateStatus(DeviceStatus.RUNNING);
      }
    }
  }

  /**
   * 检查报警条件
   * @param dataPoint 数据点
   */
  protected checkAlarms(dataPoint: IndustrialDataPoint): void {
    this.alarms.forEach((alarm, alarmId) => {
      const value = Number(dataPoint.value);
      
      if (isNaN(value)) return;
      
      let shouldTrigger = false;
      
      switch (alarm.type) {
        case 'high':
          shouldTrigger = value > alarm.threshold;
          break;
        case 'low':
          shouldTrigger = value < alarm.threshold;
          break;
        case 'high_high':
          shouldTrigger = value > alarm.threshold * 1.2;
          break;
        case 'low_low':
          shouldTrigger = value < alarm.threshold * 0.8;
          break;
      }
      
      if (shouldTrigger) {
        this.triggerAlarm(alarmId);
      } else {
        this.clearAlarm(alarmId);
      }
    });
  }

  /**
   * 更新性能指标
   */
  protected updatePerformanceMetrics(): void {
    if (!this.options.enablePerformanceTracking) return;
    
    const now = new Date();
    this.performance.timestamp = now;
    
    // 计算可用性（简化计算）
    if (this.status === DeviceStatus.RUNNING) {
      this.performance.availability = Math.min(this.performance.availability + 0.1, 100);
    } else if (this.status === DeviceStatus.ERROR || this.status === DeviceStatus.OFFLINE) {
      this.performance.availability = Math.max(this.performance.availability - 0.5, 0);
    }
    
    // 计算性能指标（简化计算）
    this.performance.performance = this.calculatePerformanceRatio();
    this.performance.quality = this.calculateQualityRatio();
    
    // 计算OEE (Overall Equipment Effectiveness)
    this.performance.oee = (this.performance.availability * this.performance.performance * this.performance.quality) / 10000;
  }

  /**
   * 计算性能比率
   * @returns 性能比率 (0-100)
   */
  protected calculatePerformanceRatio(): number {
    // 子类应该重写此方法以提供具体的性能计算
    return 85 + Math.random() * 10; // 默认85-95%
  }

  /**
   * 计算质量比率
   * @returns 质量比率 (0-100)
   */
  protected calculateQualityRatio(): number {
    // 子类应该重写此方法以提供具体的质量计算
    return 95 + Math.random() * 5; // 默认95-100%
  }

  /**
   * 启动更新循环
   */
  protected startUpdateLoop(): void {
    setInterval(() => {
      this.update();
    }, this.options.updateInterval);
  }

  /**
   * 更新设备（每帧调用）
   */
  public update(): void {
    // 更新性能指标
    if (this.options.enablePerformanceTracking) {
      this.updatePerformanceMetrics();
    }
    
    // 清理过期数据
    this.cleanupExpiredData();
    
    // 子类可以重写此方法以添加特定的更新逻辑
    this.onUpdate();
  }

  /**
   * 子类更新方法
   */
  protected abstract onUpdate(): void;

  /**
   * 清理过期数据
   */
  protected cleanupExpiredData(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    this.dataPoints.forEach((dataPoint, key) => {
      if (now - dataPoint.timestamp.getTime() > this.options.dataRetentionTime) {
        expiredKeys.push(key);
      }
    });
    
    expiredKeys.forEach(key => {
      this.dataPoints.delete(key);
    });
  }

  /**
   * 获取设备信息
   * @returns 设备信息对象
   */
  public getDeviceInfo(): any {
    return {
      deviceId: this.deviceId,
      deviceName: this.deviceName,
      deviceType: this.deviceType,
      manufacturer: this.manufacturer,
      model: this.model,
      serialNumber: this.serialNumber,
      status: this.status,
      lastStatusChange: this.lastStatusChange,
      maintenanceMode: this.maintenanceMode,
      position: this.position,
      rotation: this.rotation,
      performance: this.performance,
      activeAlarms: Array.from(this.activeAlarms),
      dataPointCount: this.dataPoints.size
    };
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    this.dataPoints.clear();
    this.alarms.clear();
    this.activeAlarms.clear();
    super.destroy();
  }
}
