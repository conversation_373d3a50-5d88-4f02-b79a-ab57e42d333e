/**
 * AI模型服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';

interface LanguageUnderstanding {
  entities: Array<{
    text: string;
    type: 'OBJECT' | 'LOCATION' | 'ATTRIBUTE' | 'ACTION';
    confidence: number;
  }>;
  intent: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  keywords: string[];
  style: string;
}

@Injectable()
export class AIModelService {
  private readonly logger = new Logger(AIModelService.name);

  constructor(
    private httpService: HttpService,
    private configService: ConfigService
  ) {}

  /**
   * 理解自然语言文本
   */
  async understandText(text: string): Promise<LanguageUnderstanding> {
    try {
      this.logger.log(`开始理解文本: ${text}`);

      // 这里可以调用外部AI服务，如OpenAI、百度AI等
      // 目前使用简化的本地实现
      const understanding = this.localTextUnderstanding(text);

      this.logger.log(`文本理解完成`);
      return understanding;

    } catch (error) {
      this.logger.error(`文本理解失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 规划场景
   */
  async planScene(understanding: LanguageUnderstanding, options: any): Promise<any> {
    try {
      this.logger.log(`开始规划场景`);

      const plan = {
        layout: this.planLayout(understanding),
        objects: this.planObjects(understanding, options),
        lighting: this.planLighting(understanding),
        materials: this.planMaterials(understanding),
        atmosphere: this.planAtmosphere(understanding)
      };

      this.logger.log(`场景规划完成`);
      return plan;

    } catch (error) {
      this.logger.error(`场景规划失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 生成场景内容
   */
  async generateSceneContent(plan: any): Promise<any> {
    try {
      this.logger.log(`开始生成场景内容`);

      const sceneData = {
        name: `Generated Scene ${Date.now()}`,
        entities: this.generateEntities(plan.objects),
        lighting: plan.lighting,
        materials: plan.materials,
        atmosphere: plan.atmosphere,
        metadata: {
          generatedAt: new Date(),
          plan
        }
      };

      this.logger.log(`场景内容生成完成`);
      return sceneData;

    } catch (error) {
      this.logger.error(`场景内容生成失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 生成预览
   */
  async generatePreview(plan: any): Promise<any> {
    try {
      this.logger.log(`开始生成预览`);

      // 生成简化的预览数据
      const previewData = {
        layout: plan.layout,
        objectCount: plan.objects?.length || 0,
        lighting: plan.lighting,
        estimatedPolygons: (plan.objects?.length || 0) * 500
      };

      this.logger.log(`预览生成完成`);
      return previewData;

    } catch (error) {
      this.logger.error(`预览生成失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 本地文本理解实现
   */
  private localTextUnderstanding(text: string): LanguageUnderstanding {
    const entities = this.extractEntities(text);
    const intent = this.classifyIntent(text);
    const sentiment = this.analyzeSentiment(text);
    const keywords = this.extractKeywords(text);
    const style = this.inferStyle(text);

    return {
      entities,
      intent,
      sentiment,
      keywords,
      style
    };
  }

  /**
   * 提取实体
   */
  private extractEntities(text: string): LanguageUnderstanding['entities'] {
    const entities: LanguageUnderstanding['entities'] = [];

    // 对象实体
    const objectPatterns = [
      /桌子|椅子|沙发|床|柜子|书架/g,
      /电脑|电视|灯|植物|花|树/g,
      /汽车|建筑|房子|门|窗户/g
    ];

    objectPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'OBJECT',
            confidence: 0.8
          });
        });
      }
    });

    // 位置实体
    const locationPatterns = [
      /办公室|客厅|卧室|厨房|浴室/g,
      /学校|图书馆|咖啡厅|餐厅|商店/g,
      /公园|广场|街道|花园/g
    ];

    locationPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'LOCATION',
            confidence: 0.9
          });
        });
      }
    });

    return entities;
  }

  /**
   * 分类意图
   */
  private classifyIntent(text: string): string {
    if (/创建|建造|制作|生成/.test(text)) return 'CREATE';
    if (/修改|改变|调整|更新/.test(text)) return 'MODIFY';
    if (/删除|移除|清除/.test(text)) return 'DELETE';
    return 'CREATE';
  }

  /**
   * 分析情感
   */
  private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['美丽', '温馨', '舒适', '明亮', '愉快', '漂亮'];
    const negativeWords = ['阴暗', '破旧', '肮脏', '混乱', '压抑'];

    const positiveCount = positiveWords.filter(word => text.includes(word)).length;
    const negativeCount = negativeWords.filter(word => text.includes(word)).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * 提取关键词
   */
  private extractKeywords(text: string): string[] {
    const words = text.split(/\s+|，|。|、/);
    return words.filter(word => word.length > 1 && !/的|了|在|和|与|或/.test(word));
  }

  /**
   * 推断风格
   */
  private inferStyle(text: string): string {
    if (/科幻|未来|太空|机器人/.test(text)) return 'scifi';
    if (/卡通|可爱|童话|动画/.test(text)) return 'cartoon';
    if (/简约|极简|现代|简洁/.test(text)) return 'minimalist';
    if (/魔法|奇幻|龙|城堡/.test(text)) return 'fantasy';
    return 'realistic';
  }

  /**
   * 规划布局
   */
  private planLayout(understanding: LanguageUnderstanding): any {
    const locations = understanding.entities.filter(e => e.type === 'LOCATION');

    if (locations.length > 0) {
      const location = locations[0].text;

      switch (location) {
        case '办公室':
          return {
            type: 'office',
            size: { width: 10, height: 3, depth: 8 },
            zones: ['work_area', 'meeting_area', 'storage_area']
          };
        case '客厅':
          return {
            type: 'living_room',
            size: { width: 12, height: 3, depth: 10 },
            zones: ['seating_area', 'entertainment_area', 'decoration_area']
          };
        default:
          return {
            type: 'generic',
            size: { width: 10, height: 3, depth: 10 },
            zones: ['main_area']
          };
      }
    }

    return {
      type: 'generic',
      size: { width: 10, height: 3, depth: 10 },
      zones: ['main_area']
    };
  }

  /**
   * 规划对象
   */
  private planObjects(understanding: LanguageUnderstanding, options: any): any[] {
    const objects = understanding.entities.filter(e => e.type === 'OBJECT');
    const plannedObjects = [];

    const maxObjects = Math.min(objects.length, options.maxObjects || 50);

    for (let i = 0; i < maxObjects; i++) {
      const obj = objects[i % objects.length];
      plannedObjects.push({
        type: obj.text,
        position: this.generateRandomPosition(),
        rotation: this.generateRandomRotation(),
        scale: this.generateRandomScale(),
        material: this.selectMaterial(obj.text)
      });
    }

    return plannedObjects;
  }

  /**
   * 生成随机位置
   */
  private generateRandomPosition(): { x: number, y: number, z: number } {
    return {
      x: (Math.random() - 0.5) * 10,
      y: 0,
      z: (Math.random() - 0.5) * 10
    };
  }

  /**
   * 生成随机旋转
   */
  private generateRandomRotation(): { x: number, y: number, z: number } {
    return {
      x: 0,
      y: Math.random() * Math.PI * 2,
      z: 0
    };
  }

  /**
   * 生成随机缩放
   */
  private generateRandomScale(): { x: number, y: number, z: number } {
    const scale = 0.8 + Math.random() * 0.4;
    return { x: scale, y: scale, z: scale };
  }

  /**
   * 选择材质
   */
  private selectMaterial(objectType: string): string {
    const materialMap: { [key: string]: string } = {
      '桌子': 'wood',
      '椅子': 'fabric',
      '沙发': 'leather',
      '电脑': 'plastic',
      '植物': 'organic',
      '灯': 'metal'
    };

    return materialMap[objectType] || 'default';
  }

  /**
   * 规划光照
   */
  private planLighting(understanding: LanguageUnderstanding): any {
    const sentiment = understanding.sentiment;

    let intensity = 1.0;
    let color = '#ffffff';

    if (sentiment === 'positive') {
      intensity = 1.2;
      color = '#fff8dc';
    } else if (sentiment === 'negative') {
      intensity = 0.6;
      color = '#e6e6fa';
    }

    return {
      ambient: { intensity: intensity * 0.3, color },
      directional: { intensity: intensity * 0.7, color }
    };
  }

  /**
   * 规划材质
   */
  private planMaterials(understanding: LanguageUnderstanding): any[] {
    const style = understanding.style;
    const materials = [];

    switch (style) {
      case 'realistic':
        materials.push(
          { name: 'wood', type: 'physical', roughness: 0.8 },
          { name: 'metal', type: 'physical', roughness: 0.2 }
        );
        break;
      case 'cartoon':
        materials.push(
          { name: 'toon', type: 'toon', color: '#ff6b6b' }
        );
        break;
      default:
        materials.push(
          { name: 'default', type: 'standard', color: '#cccccc' }
        );
    }

    return materials;
  }

  /**
   * 规划氛围
   */
  private planAtmosphere(understanding: LanguageUnderstanding): any {
    return {
      fog: understanding.sentiment === 'negative' ? 0.1 : 0.0,
      skybox: understanding.style === 'scifi' ? 'space' : 'default'
    };
  }

  /**
   * 生成实体
   */
  private generateEntities(objects: any[]): any[] {
    return objects.map((obj, index) => ({
      id: `entity_${index}`,
      name: obj.type,
      type: obj.type,
      position: obj.position,
      rotation: obj.rotation,
      scale: obj.scale,
      material: obj.material,
      components: []
    }));
  }
}
