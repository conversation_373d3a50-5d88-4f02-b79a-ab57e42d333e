/**
 * 面板注册器
 * 用于注册和管理所有面板
 */
import React from 'react';
import { TabData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import {
  AppstoreOutlined,
  BarsOutlined,
  SettingOutlined,
  FolderOutlined,
  CodeOutlined,
  PlaySquareOutlined,
  ThunderboltOutlined,
  ApartmentOutlined,
  BlockOutlined,
  CopyOutlined,
  TeamOutlined,
  ExperimentOutlined,
  DashboardOutlined,
  CloudDownloadOutlined,
  EnvironmentOutlined,
  BranchesOutlined,
  DatabaseOutlined,
  MessageOutlined,
  RobotOutlined,
  SoundOutlined,
  ApiOutlined} from '@ant-design/icons';
import { PanelType } from '../../store/ui/uiSlice';
import HierarchyPanel from './HierarchyPanel';
import InspectorPanel from './InspectorPanel';
import ScenePanel from './ScenePanel';
import AssetsPanel from './AssetsPanel';
import ConsolePanel from './ConsolePanel';
import LayersPanel from './LayersPanel';
import InstancesPanel from './InstancesPanel';
import PanelAdapter from './PanelAdapter';
import CollaborationPanel from '../collaboration/CollaborationPanel';
import UserTestingPanel from '../testing/UserTestingPanel';
import DebugPanel from '../debug/DebugPanel';
import PerformanceOptimizationPanel from '../optimization/PerformanceOptimizationPanel';
import ResourceHotUpdatePanel from '../resources/ResourceHotUpdatePanel';
import EnvironmentEditorPanel from '../environment/EnvironmentEditorPanel';
import GitPanel from '../git/GitPanel';
import KnowledgeBasePanel from './KnowledgeBasePanel';
import RAGDialoguePanel from './RAGDialoguePanel';
import NLPSceneGenerationPanel from './NLPSceneGenerationPanel';
import AvatarConfigPanel from './AvatarConfigPanel';
import VoiceConfigPanel from './VoiceConfigPanel';
import RAGApplicationsPanel from './RAGApplicationsPanel';

// 面板组件映射
const panelComponentMap: Record<string, React.ComponentType<any>> = {
  [PanelType.HIERARCHY]: HierarchyPanel,
  [PanelType.INSPECTOR]: InspectorPanel,
  [PanelType.ASSETS]: AssetsPanel,
  [PanelType.SCENE]: ScenePanel,
  [PanelType.CONSOLE]: ConsolePanel,
  [PanelType.LAYERS]: LayersPanel,
  [PanelType.INSTANCES]: InstancesPanel,
  [PanelType.COLLABORATION]: CollaborationPanel,
  [PanelType.USER_TESTING]: UserTestingPanel,
  [PanelType.DEBUG]: DebugPanel,
  [PanelType.PERFORMANCE_OPTIMIZATION]: PerformanceOptimizationPanel,
  [PanelType.RESOURCE_HOT_UPDATE]: ResourceHotUpdatePanel,
  [PanelType.ENVIRONMENT]: EnvironmentEditorPanel,
  [PanelType.GIT]: GitPanel,
  // RAG应用相关面板
  [PanelType.KNOWLEDGE_BASE]: KnowledgeBasePanel,
  [PanelType.RAG_DIALOGUE]: RAGDialoguePanel,
  // 自然语言场景生成面板
  [PanelType.NLP_SCENE_GENERATION]: NLPSceneGenerationPanel,
  [PanelType.AVATAR_CONFIG]: AvatarConfigPanel,
  [PanelType.VOICE_CONFIG]: VoiceConfigPanel,
  [PanelType.RAG_APPLICATIONS]: RAGApplicationsPanel};

// 面板图标映射
const panelIconMap: Record<string, React.ReactNode> = {
  [PanelType.HIERARCHY]: <BarsOutlined />,
  [PanelType.INSPECTOR]: <SettingOutlined />,
  [PanelType.ASSETS]: <FolderOutlined />,
  [PanelType.SCENE]: <AppstoreOutlined />,
  [PanelType.CONSOLE]: <CodeOutlined />,
  [PanelType.ANIMATION]: <PlaySquareOutlined />,
  [PanelType.PHYSICS]: <ThunderboltOutlined />,
  [PanelType.PARTICLE]: <ApartmentOutlined />,
  [PanelType.LAYERS]: <BlockOutlined />,
  [PanelType.INSTANCES]: <CopyOutlined />,
  [PanelType.COLLABORATION]: <TeamOutlined />,
  [PanelType.USER_TESTING]: <ExperimentOutlined />,
  [PanelType.DEBUG]: <DashboardOutlined />,
  [PanelType.PERFORMANCE_OPTIMIZATION]: <ThunderboltOutlined />,
  [PanelType.RESOURCE_HOT_UPDATE]: <CloudDownloadOutlined />,
  [PanelType.ENVIRONMENT]: <EnvironmentOutlined />,
  [PanelType.GIT]: <BranchesOutlined />,
  // RAG应用相关面板图标
  [PanelType.KNOWLEDGE_BASE]: <DatabaseOutlined />,
  [PanelType.RAG_DIALOGUE]: <MessageOutlined />,
  [PanelType.AVATAR_CONFIG]: <RobotOutlined />,
  [PanelType.VOICE_CONFIG]: <SoundOutlined />,
  [PanelType.RAG_APPLICATIONS]: <ApiOutlined />,
  // 自然语言场景生成面板图标
  [PanelType.NLP_SCENE_GENERATION]: <RobotOutlined />};

/**
 * 创建面板标题组件
 * @param type 面板类型
 * @returns 面板标题组件
 */
export const createPanelTitle = (type: string): React.ReactElement => {
  const { t } = useTranslation();

  return (
    <div className="panel-title-container">
      {panelIconMap[type] || <AppstoreOutlined />}
      <span className="panel-title-text">
        {t(`editor.${type}View`)}
      </span>
    </div>
  );
};

/**
 * 创建面板内容组件
 * @param type 面板类型
 * @returns 面板内容组件
 */
export const createPanelContent = (type: string): React.ReactElement => {
  const PanelComponent = panelComponentMap[type];

  if (!PanelComponent) {
    return <div>未知面板类型: {type}</div>;
  }

  return (
    <PanelAdapter panelType={type}>
      <PanelComponent />
    </PanelAdapter>
  );
};

/**
 * 创建面板标签数据
 * @param type 面板类型
 * @param closable 是否可关闭
 * @returns 面板标签数据
 */
export const createPanelTabData = (type: string, closable: boolean = true): TabData => {
  return {
    id: type,
    title: createPanelTitle(type),
    content: createPanelContent(type),
    closable,
    group: type};
};

/**
 * 注册面板组件
 * @param type 面板类型
 * @param component 面板组件
 */
export const registerPanelComponent = (type: string, component: React.ComponentType<any>): void => {
  panelComponentMap[type] = component;
};

/**
 * 注册面板图标
 * @param type 面板类型
 * @param icon 面板图标
 */
export const registerPanelIcon = (type: string, icon: React.ReactNode): void => {
  panelIconMap[type] = icon;
};

/**
 * 获取面板组件
 * @param type 面板类型
 * @returns 面板组件
 */
export const getPanelComponent = (type: string): React.ComponentType<any> | undefined => {
  return panelComponentMap[type];
};

/**
 * 获取面板图标
 * @param type 面板类型
 * @returns 面板图标
 */
export const getPanelIcon = (type: string): React.ReactNode => {
  return panelIconMap[type] || <AppstoreOutlined />;
};

/**
 * 获取所有已注册的面板类型
 * @returns 面板类型数组
 */
export const getAllPanelTypes = (): string[] => {
  return Object.keys(panelComponentMap);
};

// 预定义的面板标签数据
export const HierarchyPanelTab = createPanelTabData(PanelType.HIERARCHY, false);
export const InspectorPanelTab = createPanelTabData(PanelType.INSPECTOR, false);
export const ScenePanelTab = createPanelTabData(PanelType.SCENE, false);
export const AssetsPanelTab = createPanelTabData(PanelType.ASSETS, false);
export const ConsolePanelTab = createPanelTabData(PanelType.CONSOLE, true);
export const LayersPanelTab = createPanelTabData(PanelType.LAYERS, true);
export const InstancesPanelTab = createPanelTabData(PanelType.INSTANCES, true);
export const CollaborationPanelTab = createPanelTabData(PanelType.COLLABORATION, true);
export const UserTestingPanelTab = createPanelTabData(PanelType.USER_TESTING, true);
export const DebugPanelTab = createPanelTabData(PanelType.DEBUG, true);
export const PerformanceOptimizationPanelTab = createPanelTabData(PanelType.PERFORMANCE_OPTIMIZATION, true);
export const ResourceHotUpdatePanelTab = createPanelTabData(PanelType.RESOURCE_HOT_UPDATE, true);
export const EnvironmentPanelTab = createPanelTabData(PanelType.ENVIRONMENT, true);

// RAG应用相关面板标签
export const KnowledgeBasePanelTab = createPanelTabData(PanelType.KNOWLEDGE_BASE, true);
export const RAGDialoguePanelTab = createPanelTabData(PanelType.RAG_DIALOGUE, true);

// 自然语言场景生成面板标签
export const NLPSceneGenerationPanelTab = createPanelTabData(PanelType.NLP_SCENE_GENERATION, true);
