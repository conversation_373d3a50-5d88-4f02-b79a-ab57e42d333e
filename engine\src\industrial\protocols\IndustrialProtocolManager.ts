import { EventEmitter } from 'events';
import { 
  IndustrialProtocol, 
  ProtocolType, 
  DeviceConfig, 
  DeviceConnection, 
  IndustrialDataPoint,
  DeviceStatus 
} from '../types';
import { Debug } from '../../core/Debug';

/**
 * 工业通信协议管理器
 * 负责管理多种工业通信协议的连接和数据交换
 */
export class IndustrialProtocolManager extends EventEmitter {
  private protocols: Map<ProtocolType, IndustrialProtocol> = new Map();
  private connections: Map<string, DeviceConnection> = new Map();
  private subscriptions: Map<string, { deviceId: string; tagIds: string[]; callback: Function }> = new Map();
  private dataCache: Map<string, IndustrialDataPoint> = new Map();
  private reconnectTimers: Map<string, NodeJS.Timeout> = new Map();
  
  // 配置选项
  private options = {
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    dataRetentionTime: 300000, // 5分钟
    enableDataCaching: true,
    enableAutoReconnect: true
  };

  constructor(options?: Partial<typeof IndustrialProtocolManager.prototype.options>) {
    super();
    if (options) {
      this.options = { ...this.options, ...options };
    }
    
    // 启动数据清理定时器
    this.startDataCleanup();
  }

  /**
   * 注册工业通信协议
   * @param type 协议类型
   * @param protocol 协议实现
   */
  public registerProtocol(type: ProtocolType, protocol: IndustrialProtocol): void {
    this.protocols.set(type, protocol);
    Debug.log('IndustrialProtocolManager', `已注册协议: ${type}`);
  }

  /**
   * 连接设备
   * @param deviceConfig 设备配置
   * @returns 设备连接信息
   */
  public async connectDevice(deviceConfig: DeviceConfig): Promise<DeviceConnection> {
    const protocol = this.protocols.get(deviceConfig.protocol);
    if (!protocol) {
      throw new Error(`不支持的协议类型: ${deviceConfig.protocol}`);
    }

    try {
      Debug.log('IndustrialProtocolManager', `正在连接设备: ${deviceConfig.name} (${deviceConfig.id})`);
      
      const connection = await protocol.connect(deviceConfig);
      this.connections.set(deviceConfig.id, connection);
      
      // 发送连接成功事件
      this.emit('deviceConnected', connection);
      
      Debug.log('IndustrialProtocolManager', `设备连接成功: ${deviceConfig.name}`);
      return connection;
      
    } catch (error) {
      Debug.error('IndustrialProtocolManager', `设备连接失败: ${deviceConfig.name}`, error);
      
      // 如果启用自动重连，则设置重连定时器
      if (this.options.enableAutoReconnect) {
        this.scheduleReconnect(deviceConfig);
      }
      
      throw error;
    }
  }

  /**
   * 断开设备连接
   * @param deviceId 设备ID
   */
  public async disconnectDevice(deviceId: string): Promise<void> {
    const connection = this.connections.get(deviceId);
    if (!connection) {
      Debug.warn('IndustrialProtocolManager', `设备未连接: ${deviceId}`);
      return;
    }

    const protocol = this.protocols.get(connection.protocol);
    if (protocol) {
      try {
        await protocol.disconnect(deviceId);
        Debug.log('IndustrialProtocolManager', `设备断开连接: ${deviceId}`);
      } catch (error) {
        Debug.error('IndustrialProtocolManager', `断开设备连接失败: ${deviceId}`, error);
      }
    }

    // 清理连接信息
    this.connections.delete(deviceId);
    
    // 取消重连定时器
    const timer = this.reconnectTimers.get(deviceId);
    if (timer) {
      clearTimeout(timer);
      this.reconnectTimers.delete(deviceId);
    }

    // 发送断开连接事件
    this.emit('deviceDisconnected', { deviceId });
  }

  /**
   * 读取设备标签数据
   * @param deviceId 设备ID
   * @param tagId 标签ID
   * @returns 工业数据点
   */
  public async readTag(deviceId: string, tagId: string): Promise<IndustrialDataPoint> {
    const connection = this.connections.get(deviceId);
    if (!connection || connection.status !== DeviceStatus.ONLINE) {
      throw new Error(`设备未连接或状态异常: ${deviceId}`);
    }

    const protocol = this.protocols.get(connection.protocol);
    if (!protocol) {
      throw new Error(`协议未找到: ${connection.protocol}`);
    }

    try {
      const dataPoint = await protocol.readTag(deviceId, tagId);
      
      // 缓存数据
      if (this.options.enableDataCaching) {
        const cacheKey = `${deviceId}:${tagId}`;
        this.dataCache.set(cacheKey, dataPoint);
      }
      
      // 发送数据更新事件
      this.emit('dataReceived', dataPoint);
      
      return dataPoint;
      
    } catch (error) {
      Debug.error('IndustrialProtocolManager', `读取标签失败: ${deviceId}:${tagId}`, error);
      throw error;
    }
  }

  /**
   * 写入设备标签数据
   * @param deviceId 设备ID
   * @param tagId 标签ID
   * @param value 写入值
   * @returns 是否写入成功
   */
  public async writeTag(deviceId: string, tagId: string, value: any): Promise<boolean> {
    const connection = this.connections.get(deviceId);
    if (!connection || connection.status !== DeviceStatus.ONLINE) {
      throw new Error(`设备未连接或状态异常: ${deviceId}`);
    }

    const protocol = this.protocols.get(connection.protocol);
    if (!protocol) {
      throw new Error(`协议未找到: ${connection.protocol}`);
    }

    try {
      const success = await protocol.writeTag(deviceId, tagId, value);
      
      // 发送写入事件
      this.emit('dataWritten', { deviceId, tagId, value, success });
      
      return success;
      
    } catch (error) {
      Debug.error('IndustrialProtocolManager', `写入标签失败: ${deviceId}:${tagId}`, error);
      throw error;
    }
  }

  /**
   * 批量读取设备标签
   * @param deviceId 设备ID
   * @param tagIds 标签ID列表
   * @returns 工业数据点数组
   */
  public async readMultipleTags(deviceId: string, tagIds: string[]): Promise<IndustrialDataPoint[]> {
    const connection = this.connections.get(deviceId);
    if (!connection || connection.status !== DeviceStatus.ONLINE) {
      throw new Error(`设备未连接或状态异常: ${deviceId}`);
    }

    const protocol = this.protocols.get(connection.protocol);
    if (!protocol) {
      throw new Error(`协议未找到: ${connection.protocol}`);
    }

    try {
      const dataPoints = await protocol.readMultipleTags(deviceId, tagIds);
      
      // 缓存数据
      if (this.options.enableDataCaching) {
        dataPoints.forEach(dataPoint => {
          const cacheKey = `${deviceId}:${dataPoint.tagId}`;
          this.dataCache.set(cacheKey, dataPoint);
        });
      }
      
      // 发送批量数据更新事件
      this.emit('batchDataReceived', dataPoints);
      
      return dataPoints;
      
    } catch (error) {
      Debug.error('IndustrialProtocolManager', `批量读取标签失败: ${deviceId}`, error);
      throw error;
    }
  }

  /**
   * 订阅设备数据
   * @param deviceId 设备ID
   * @param tagIds 标签ID列表
   * @param callback 数据回调函数
   * @returns 订阅ID
   */
  public async subscribeData(
    deviceId: string, 
    tagIds: string[], 
    callback: (data: IndustrialDataPoint[]) => void
  ): Promise<string> {
    const connection = this.connections.get(deviceId);
    if (!connection || connection.status !== DeviceStatus.ONLINE) {
      throw new Error(`设备未连接或状态异常: ${deviceId}`);
    }

    const protocol = this.protocols.get(connection.protocol);
    if (!protocol) {
      throw new Error(`协议未找到: ${connection.protocol}`);
    }

    try {
      const subscriptionId = await protocol.subscribe(deviceId, tagIds, callback);
      
      // 存储订阅信息
      this.subscriptions.set(subscriptionId, { deviceId, tagIds, callback });
      
      Debug.log('IndustrialProtocolManager', `数据订阅成功: ${subscriptionId}`);
      return subscriptionId;
      
    } catch (error) {
      Debug.error('IndustrialProtocolManager', `数据订阅失败: ${deviceId}`, error);
      throw error;
    }
  }

  /**
   * 取消数据订阅
   * @param subscriptionId 订阅ID
   */
  public async unsubscribeData(subscriptionId: string): Promise<void> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) {
      Debug.warn('IndustrialProtocolManager', `订阅未找到: ${subscriptionId}`);
      return;
    }

    const connection = this.connections.get(subscription.deviceId);
    if (connection) {
      const protocol = this.protocols.get(connection.protocol);
      if (protocol) {
        try {
          await protocol.unsubscribe(subscriptionId);
        } catch (error) {
          Debug.error('IndustrialProtocolManager', `取消订阅失败: ${subscriptionId}`, error);
        }
      }
    }

    // 清理订阅信息
    this.subscriptions.delete(subscriptionId);
    Debug.log('IndustrialProtocolManager', `取消订阅: ${subscriptionId}`);
  }

  /**
   * 获取设备连接状态
   * @param deviceId 设备ID
   * @returns 设备连接信息
   */
  public getDeviceConnection(deviceId: string): DeviceConnection | undefined {
    return this.connections.get(deviceId);
  }

  /**
   * 获取所有设备连接状态
   * @returns 设备连接信息数组
   */
  public getAllConnections(): DeviceConnection[] {
    return Array.from(this.connections.values());
  }

  /**
   * 获取缓存的数据
   * @param deviceId 设备ID
   * @param tagId 标签ID
   * @returns 缓存的数据点
   */
  public getCachedData(deviceId: string, tagId: string): IndustrialDataPoint | undefined {
    const cacheKey = `${deviceId}:${tagId}`;
    return this.dataCache.get(cacheKey);
  }

  /**
   * 安排设备重连
   * @param deviceConfig 设备配置
   */
  private scheduleReconnect(deviceConfig: DeviceConfig): void {
    const existingTimer = this.reconnectTimers.get(deviceConfig.id);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const timer = setTimeout(async () => {
      try {
        await this.connectDevice(deviceConfig);
      } catch (error) {
        Debug.error('IndustrialProtocolManager', `重连失败: ${deviceConfig.name}`, error);
        // 继续尝试重连
        this.scheduleReconnect(deviceConfig);
      }
    }, this.options.reconnectInterval);

    this.reconnectTimers.set(deviceConfig.id, timer);
  }

  /**
   * 启动数据清理定时器
   */
  private startDataCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      const expiredKeys: string[] = [];

      this.dataCache.forEach((dataPoint, key) => {
        if (now - dataPoint.timestamp.getTime() > this.options.dataRetentionTime) {
          expiredKeys.push(key);
        }
      });

      expiredKeys.forEach(key => {
        this.dataCache.delete(key);
      });

      if (expiredKeys.length > 0) {
        Debug.log('IndustrialProtocolManager', `清理过期数据: ${expiredKeys.length} 条`);
      }
    }, this.options.dataRetentionTime);
  }

  /**
   * 销毁管理器
   */
  public async destroy(): Promise<void> {
    // 断开所有设备连接
    const disconnectPromises = Array.from(this.connections.keys()).map(deviceId => 
      this.disconnectDevice(deviceId)
    );
    await Promise.all(disconnectPromises);

    // 清理定时器
    this.reconnectTimers.forEach(timer => clearTimeout(timer));
    this.reconnectTimers.clear();

    // 清理缓存
    this.dataCache.clear();
    this.subscriptions.clear();
    this.connections.clear();
    this.protocols.clear();

    Debug.log('IndustrialProtocolManager', '协议管理器已销毁');
  }
}
