# 自然语言生成场景功能部署测试指南

## 概述

本指南详细说明了自然语言生成场景功能的部署流程、测试方法和运维监控，确保功能能够稳定可靠地运行在生产环境中。

## 一、部署前准备

### 1.1 环境要求

#### 硬件要求
- **CPU**: 最低4核，推荐8核以上
- **内存**: 最低8GB，推荐16GB以上
- **存储**: 最低100GB可用空间，推荐SSD
- **网络**: 稳定的网络连接，带宽不低于100Mbps

#### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Node.js**: 18.0+ (开发环境)
- **MySQL**: 8.0+
- **Redis**: 6.0+

### 1.2 依赖服务检查

```bash
#!/bin/bash
# scripts/check-dependencies.sh

echo "检查部署环境..."

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装"
    exit 1
fi
echo "✅ Docker 已安装: $(docker --version)"

# 检查Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装"
    exit 1
fi
echo "✅ Docker Compose 已安装: $(docker-compose --version)"

# 检查端口占用
check_port() {
    if netstat -tuln | grep -q ":$1 "; then
        echo "❌ 端口 $1 已被占用"
        return 1
    else
        echo "✅ 端口 $1 可用"
        return 0
    fi
}

check_port 3009  # NLP场景服务
check_port 3306  # MySQL
check_port 6379  # Redis
check_port 8000  # AI模型服务

echo "环境检查完成"
```

### 1.3 配置文件准备

```bash
# 创建配置目录
mkdir -p config/nlp-scene-service
mkdir -p config/ai-models
mkdir -p data/mysql
mkdir -p data/redis
mkdir -p logs

# 复制配置文件模板
cp server/nlp-scene-service/.env.example config/nlp-scene-service/.env
cp docker-compose.nlp.yml.example docker-compose.nlp.yml
```

## 二、分步部署流程

### 2.1 第一步：基础设施部署

```bash
#!/bin/bash
# scripts/deploy-infrastructure.sh

echo "部署基础设施..."

# 创建Docker网络
docker network create dlengine-network 2>/dev/null || echo "网络已存在"

# 启动MySQL
docker run -d \
  --name dlengine-mysql \
  --network dlengine-network \
  -e MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD} \
  -e MYSQL_DATABASE=dlengine \
  -e MYSQL_USER=dlengine \
  -e MYSQL_PASSWORD=${DB_PASSWORD} \
  -v $(pwd)/data/mysql:/var/lib/mysql \
  -p 3306:3306 \
  mysql:8.0

# 启动Redis
docker run -d \
  --name dlengine-redis \
  --network dlengine-network \
  -v $(pwd)/data/redis:/data \
  -p 6379:6379 \
  redis:7-alpine

# 等待服务启动
echo "等待数据库启动..."
sleep 30

# 检查服务状态
docker ps | grep dlengine-mysql
docker ps | grep dlengine-redis

echo "基础设施部署完成"
```

### 2.2 第二步：AI模型服务部署

```bash
#!/bin/bash
# scripts/deploy-ai-models.sh

echo "部署AI模型服务..."

# 下载预训练模型（示例）
if [ ! -d "ai-models/nlp_scene_model" ]; then
    echo "下载NLP场景生成模型..."
    mkdir -p ai-models/nlp_scene_model/1
    
    # 这里应该下载实际的模型文件
    # wget -O ai-models/nlp_scene_model/1/model.pb https://example.com/models/nlp_scene_model.pb
    
    # 创建模型配置文件
    cat > ai-models/nlp_scene_model/1/model.config << EOF
model_config_list {
  config {
    name: 'nlp_scene_model'
    base_path: '/models/nlp_scene_model'
    model_platform: 'tensorflow'
    model_version_policy {
      latest {
        num_versions: 1
      }
    }
  }
}
EOF
fi

# 启动AI模型服务
docker run -d \
  --name dlengine-ai-models \
  --network dlengine-network \
  -p 8000:8501 \
  -v $(pwd)/ai-models:/models \
  -e MODEL_NAME=nlp_scene_model \
  tensorflow/serving:latest

echo "AI模型服务部署完成"
```

### 2.3 第三步：NLP场景服务部署

```bash
#!/bin/bash
# scripts/deploy-nlp-service.sh

echo "部署NLP场景生成服务..."

# 构建服务镜像
cd server/nlp-scene-service
docker build -t dlengine/nlp-scene-service:latest .
cd ../..

# 启动NLP场景服务
docker run -d \
  --name dlengine-nlp-scene-service \
  --network dlengine-network \
  -p 3009:3009 \
  -e NODE_ENV=production \
  -e DB_HOST=dlengine-mysql \
  -e DB_PORT=3306 \
  -e DB_USERNAME=dlengine \
  -e DB_PASSWORD=${DB_PASSWORD} \
  -e DB_DATABASE=dlengine \
  -e REDIS_HOST=dlengine-redis \
  -e REDIS_PORT=6379 \
  -e AI_MODEL_ENDPOINT=http://dlengine-ai-models:8501 \
  -v $(pwd)/data/scenes:/app/storage/scenes \
  -v $(pwd)/logs:/app/logs \
  dlengine/nlp-scene-service:latest

echo "NLP场景服务部署完成"
```

### 2.4 第四步：前端编辑器更新

```bash
#!/bin/bash
# scripts/deploy-editor.sh

echo "更新编辑器..."

# 进入编辑器目录
cd editor

# 安装新依赖
npm install

# 构建编辑器
npm run build

# 重启编辑器服务（如果使用Docker）
if docker ps | grep -q dlengine-editor; then
    docker restart dlengine-editor
fi

echo "编辑器更新完成"
```

## 三、自动化部署脚本

### 3.1 一键部署脚本

```bash
#!/bin/bash
# scripts/deploy-all.sh

set -e  # 遇到错误立即退出

echo "🚀 开始部署自然语言生成场景功能..."

# 检查环境变量
if [ -z "$DB_PASSWORD" ] || [ -z "$DB_ROOT_PASSWORD" ]; then
    echo "❌ 请设置必要的环境变量: DB_PASSWORD, DB_ROOT_PASSWORD"
    exit 1
fi

# 1. 检查依赖
echo "📋 检查部署环境..."
./scripts/check-dependencies.sh

# 2. 部署基础设施
echo "🏗️ 部署基础设施..."
./scripts/deploy-infrastructure.sh

# 3. 部署AI模型服务
echo "🤖 部署AI模型服务..."
./scripts/deploy-ai-models.sh

# 4. 部署NLP场景服务
echo "🧠 部署NLP场景服务..."
./scripts/deploy-nlp-service.sh

# 5. 更新编辑器
echo "🎨 更新编辑器..."
./scripts/deploy-editor.sh

# 6. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 60

# 7. 健康检查
echo "🔍 执行健康检查..."
./scripts/health-check.sh

echo "✅ 部署完成！"
echo "📊 服务状态:"
echo "  - NLP场景服务: http://localhost:3009"
echo "  - API文档: http://localhost:3009/api/docs"
echo "  - 编辑器: http://localhost:3000"
```

### 3.2 健康检查脚本

```bash
#!/bin/bash
# scripts/health-check.sh

echo "执行健康检查..."

# 检查服务状态
check_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1

    echo "检查 $service_name..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null; then
            echo "✅ $service_name 运行正常"
            return 0
        fi
        
        echo "⏳ 等待 $service_name 启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service_name 启动失败"
    return 1
}

# 检查各个服务
check_service "MySQL" "mysql://dlengine:${DB_PASSWORD}@localhost:3306/dlengine" || exit 1
check_service "Redis" "redis://localhost:6379" || exit 1
check_service "AI模型服务" "http://localhost:8000/v1/models/nlp_scene_model" || exit 1
check_service "NLP场景服务" "http://localhost:3009/health" || exit 1

# 功能测试
echo "执行功能测试..."
response=$(curl -s -X POST http://localhost:3009/api/v1/nlp-scene/preview \
  -H "Content-Type: application/json" \
  -d '{
    "text": "创建一个简单的房间",
    "style": "realistic",
    "lowQuality": true
  }')

if echo "$response" | grep -q '"success":true'; then
    echo "✅ 功能测试通过"
else
    echo "❌ 功能测试失败"
    echo "响应: $response"
    exit 1
fi

echo "🎉 所有检查通过！"
```

## 四、测试策略

### 4.1 单元测试

```bash
#!/bin/bash
# scripts/run-unit-tests.sh

echo "运行单元测试..."

# 测试引擎层
cd engine
npm test -- --coverage
cd ..

# 测试编辑器
cd editor
npm test -- --coverage
cd ..

# 测试服务器端
cd server/nlp-scene-service
npm test -- --coverage
cd ../..

echo "单元测试完成"
```

### 4.2 集成测试

```typescript
// tests/integration/nlp-scene-integration.test.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../server/nlp-scene-service/src/app.module';

describe('NLP场景生成集成测试', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('场景生成流程', () => {
    it('应该成功生成简单场景', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/nlp-scene/generate')
        .send({
          text: '创建一个现代办公室',
          style: 'realistic',
          quality: 50,
          maxObjects: 20,
          userId: 'test-user'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.sceneData).toBeDefined();
      expect(response.body.data.understanding).toBeDefined();
    });

    it('应该成功生成预览', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/nlp-scene/preview')
        .send({
          text: '创建一个客厅',
          style: 'minimalist',
          lowQuality: true
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.previewData).toBeDefined();
    });
  });

  describe('错误处理', () => {
    it('应该处理无效输入', async () => {
      await request(app.getHttpServer())
        .post('/api/v1/nlp-scene/generate')
        .send({
          text: '',
          style: 'realistic',
          quality: 50,
          maxObjects: 20,
          userId: 'test-user'
        })
        .expect(400);
    });
  });
});
```

### 4.3 端到端测试

```typescript
// tests/e2e/nlp-scene-e2e.test.ts
import { test, expect } from '@playwright/test';

test.describe('自然语言场景生成端到端测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000/editor');
    
    // 等待编辑器加载
    await page.waitForSelector('[data-testid="editor-main"]');
  });

  test('完整的场景生成工作流', async ({ page }) => {
    // 1. 打开NLP面板
    await page.click('[data-testid="nlp-panel-button"]');
    await page.waitForSelector('[data-testid="nlp-scene-panel"]');

    // 2. 输入场景描述
    await page.fill(
      '[data-testid="scene-description-input"]',
      '创建一个温馨的咖啡厅，有木质桌椅和绿色植物'
    );

    // 3. 设置参数
    await page.selectOption('[data-testid="style-select"]', 'realistic');
    await page.fill('[data-testid="quality-slider"]', '70');

    // 4. 生成场景
    await page.click('[data-testid="generate-button"]');

    // 5. 等待生成完成
    await page.waitForSelector('[data-testid="generation-complete"]', {
      timeout: 120000 // 2分钟超时
    });

    // 6. 验证结果
    const scenePreview = page.locator('[data-testid="scene-preview"]');
    await expect(scenePreview).toBeVisible();

    // 7. 检查历史记录
    const historyItems = page.locator('[data-testid="history-item"]');
    await expect(historyItems).toHaveCount(1);

    // 8. 预览场景
    await page.click('[data-testid="preview-button"]');
    const previewModal = page.locator('[data-testid="preview-modal"]');
    await expect(previewModal).toBeVisible();
  });

  test('性能测试', async ({ page }) => {
    // 测试多个并发生成请求
    const promises = [];
    
    for (let i = 0; i < 3; i++) {
      promises.push(
        page.evaluate(async (index) => {
          const response = await fetch('/api/v1/nlp-scene/preview', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              text: `创建房间${index}`,
              style: 'realistic',
              lowQuality: true
            })
          });
          return response.json();
        }, i)
      );
    }

    const results = await Promise.all(promises);
    
    // 验证所有请求都成功
    results.forEach(result => {
      expect(result.success).toBe(true);
    });
  });
});
```

## 五、监控和运维

### 5.1 监控配置

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'nlp-scene-service'
    static_configs:
      - targets: ['localhost:3009']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'mysql'
    static_configs:
      - targets: ['localhost:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']
```

### 5.2 告警规则

```yaml
# monitoring/alert-rules.yml
groups:
  - name: nlp-scene-service
    rules:
      - alert: NLPServiceDown
        expr: up{job="nlp-scene-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "NLP场景服务不可用"
          description: "NLP场景生成服务已停止响应超过1分钟"

      - alert: HighGenerationLatency
        expr: nlp_scene_generation_duration_seconds > 60
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "场景生成延迟过高"
          description: "场景生成时间超过60秒"

      - alert: HighErrorRate
        expr: rate(nlp_scene_generations_total{status="error"}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "场景生成错误率过高"
          description: "场景生成错误率超过10%"
```

### 5.3 日志管理

```bash
#!/bin/bash
# scripts/setup-logging.sh

echo "配置日志管理..."

# 创建日志轮转配置
cat > /etc/logrotate.d/dlengine-nlp << EOF
/var/log/dlengine/nlp-scene-service/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 dlengine dlengine
    postrotate
        docker kill -s USR1 dlengine-nlp-scene-service 2>/dev/null || true
    endscript
}
EOF

# 配置rsyslog
cat > /etc/rsyslog.d/50-dlengine.conf << EOF
# DL引擎日志配置
:programname, isequal, "dlengine-nlp" /var/log/dlengine/nlp-scene-service/app.log
& stop
EOF

systemctl restart rsyslog

echo "日志管理配置完成"
```

## 总结

本部署测试指南提供了完整的自然语言生成场景功能部署流程，包括：

1. **部署前准备**：环境检查和配置文件准备
2. **分步部署**：基础设施、AI模型、服务和前端的逐步部署
3. **自动化脚本**：一键部署和健康检查脚本
4. **测试策略**：单元测试、集成测试和端到端测试
5. **监控运维**：性能监控、告警配置和日志管理

通过遵循本指南，可以确保自然语言生成场景功能能够稳定可靠地部署到生产环境中。
