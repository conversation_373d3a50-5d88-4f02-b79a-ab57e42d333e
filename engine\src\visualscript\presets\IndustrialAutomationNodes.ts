import { VisualScriptNode } from '../VisualScriptNode';
import { NodeCategory } from '../NodeRegistry';
import { NodeRegistry } from '../NodeRegistry';

/**
 * 工业设备连接节点
 * 连接到工业设备并建立通信
 */
export class IndustrialDeviceConnectNode extends VisualScriptNode {
  constructor() {
    super();
    this.title = '工业设备连接';
    this.category = NodeCategory.INDUSTRIAL;
    
    // 输入插槽
    this.addInputSlot('trigger', '触发', 'exec');
    this.addInputSlot('deviceConfig', '设备配置', 'object');
    this.addInputSlot('protocol', '通信协议', 'string');
    this.addInputSlot('address', '设备地址', 'string');
    this.addInputSlot('port', '端口', 'number');
    
    // 输出插槽
    this.addOutputSlot('success', '成功', 'exec');
    this.addOutputSlot('fail', '失败', 'exec');
    this.addOutputSlot('connection', '连接对象', 'object');
    this.addOutputSlot('deviceId', '设备ID', 'string');
  }

  async execute(): Promise<any> {
    const deviceConfig = this.getInputValue('deviceConfig') as any;
    const protocol = this.getInputValue('protocol') as string;
    const address = this.getInputValue('address') as string;
    const port = this.getInputValue('port') as number;

    try {
      // 模拟工业设备连接
      const connection = {
        deviceId: deviceConfig?.id || `device_${Date.now()}`,
        protocol: protocol || 'modbus_tcp',
        address: address || '*************',
        port: port || 502,
        connected: true,
        status: 'online',
        lastConnected: new Date(),
        
        // 模拟设备方法
        readTag: async (tagId: string) => {
          return {
            tagId,
            value: Math.random() * 100,
            timestamp: new Date(),
            quality: 'good'
          };
        },
        
        writeTag: async (tagId: string, value: any) => {
          console.log(`写入标签 ${tagId}: ${value}`);
          return true;
        },
        
        disconnect: async () => {
          console.log('设备连接已断开');
        }
      };

      this.setOutputValue('connection', connection);
      this.setOutputValue('deviceId', connection.deviceId);
      this.triggerFlow('success');
      
      return connection;
    } catch (error) {
      console.error('工业设备连接失败:', error);
      this.triggerFlow('fail');
      return null;
    }
  }
}

/**
 * 读取设备标签节点
 * 从工业设备读取数据标签
 */
export class ReadDeviceTagNode extends VisualScriptNode {
  constructor() {
    super();
    this.title = '读取设备标签';
    this.category = NodeCategory.INDUSTRIAL;
    
    // 输入插槽
    this.addInputSlot('trigger', '触发', 'exec');
    this.addInputSlot('connection', '设备连接', 'object');
    this.addInputSlot('tagId', '标签ID', 'string');
    this.addInputSlot('dataType', '数据类型', 'string');
    
    // 输出插槽
    this.addOutputSlot('success', '成功', 'exec');
    this.addOutputSlot('fail', '失败', 'exec');
    this.addOutputSlot('value', '读取值', 'any');
    this.addOutputSlot('timestamp', '时间戳', 'string');
    this.addOutputSlot('quality', '数据质量', 'string');
  }

  async execute(): Promise<any> {
    const connection = this.getInputValue('connection') as any;
    const tagId = this.getInputValue('tagId') as string;
    const dataType = this.getInputValue('dataType') as string;

    if (!connection || !tagId) {
      this.triggerFlow('fail');
      return null;
    }

    try {
      const result = await connection.readTag(tagId);
      
      // 根据数据类型转换值
      let convertedValue = result.value;
      switch (dataType) {
        case 'boolean':
          convertedValue = Boolean(result.value);
          break;
        case 'integer':
          convertedValue = Math.floor(Number(result.value));
          break;
        case 'float':
          convertedValue = Number(result.value);
          break;
        case 'string':
          convertedValue = String(result.value);
          break;
      }

      this.setOutputValue('value', convertedValue);
      this.setOutputValue('timestamp', result.timestamp.toISOString());
      this.setOutputValue('quality', result.quality);
      this.triggerFlow('success');
      
      return convertedValue;
    } catch (error) {
      console.error('读取设备标签失败:', error);
      this.triggerFlow('fail');
      return null;
    }
  }
}

/**
 * 写入设备标签节点
 * 向工业设备写入数据标签
 */
export class WriteDeviceTagNode extends VisualScriptNode {
  constructor() {
    super();
    this.title = '写入设备标签';
    this.category = NodeCategory.INDUSTRIAL;
    
    // 输入插槽
    this.addInputSlot('trigger', '触发', 'exec');
    this.addInputSlot('connection', '设备连接', 'object');
    this.addInputSlot('tagId', '标签ID', 'string');
    this.addInputSlot('value', '写入值', 'any');
    this.addInputSlot('dataType', '数据类型', 'string');
    
    // 输出插槽
    this.addOutputSlot('success', '成功', 'exec');
    this.addOutputSlot('fail', '失败', 'exec');
    this.addOutputSlot('result', '写入结果', 'boolean');
  }

  async execute(): Promise<any> {
    const connection = this.getInputValue('connection') as any;
    const tagId = this.getInputValue('tagId') as string;
    const value = this.getInputValue('value');
    const dataType = this.getInputValue('dataType') as string;

    if (!connection || !tagId || value === undefined) {
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 根据数据类型转换值
      let convertedValue = value;
      switch (dataType) {
        case 'boolean':
          convertedValue = Boolean(value);
          break;
        case 'integer':
          convertedValue = Math.floor(Number(value));
          break;
        case 'float':
          convertedValue = Number(value);
          break;
        case 'string':
          convertedValue = String(value);
          break;
      }

      const result = await connection.writeTag(tagId, convertedValue);
      
      this.setOutputValue('result', result);
      this.triggerFlow(result ? 'success' : 'fail');
      
      return result;
    } catch (error) {
      console.error('写入设备标签失败:', error);
      this.setOutputValue('result', false);
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 设备状态监控节点
 * 监控工业设备的运行状态
 */
export class DeviceStatusMonitorNode extends VisualScriptNode {
  private monitorTimer: NodeJS.Timeout | null = null;

  constructor() {
    super();
    this.title = '设备状态监控';
    this.category = NodeCategory.INDUSTRIAL;
    
    // 输入插槽
    this.addInputSlot('start', '开始监控', 'exec');
    this.addInputSlot('stop', '停止监控', 'exec');
    this.addInputSlot('connection', '设备连接', 'object');
    this.addInputSlot('interval', '监控间隔(秒)', 'number');
    this.addInputSlot('statusTags', '状态标签列表', 'array');
    
    // 输出插槽
    this.addOutputSlot('statusChanged', '状态变更', 'exec');
    this.addOutputSlot('online', '设备在线', 'exec');
    this.addOutputSlot('offline', '设备离线', 'exec');
    this.addOutputSlot('error', '设备故障', 'exec');
    this.addOutputSlot('status', '当前状态', 'string');
    this.addOutputSlot('statusData', '状态数据', 'object');
  }

  async execute(): Promise<any> {
    const flowType = this.getActiveFlow();
    const connection = this.getInputValue('connection') as any;
    const interval = this.getInputValue('interval') as number || 5;
    const statusTags = this.getInputValue('statusTags') as string[] || [];

    if (flowType === 'start') {
      if (!connection) {
        console.error('设备连接对象为空');
        return;
      }

      // 停止现有监控
      if (this.monitorTimer) {
        clearInterval(this.monitorTimer);
      }

      // 开始监控
      this.monitorTimer = setInterval(async () => {
        try {
          const statusData: any = {};
          
          // 读取所有状态标签
          for (const tagId of statusTags) {
            const result = await connection.readTag(tagId);
            statusData[tagId] = result;
          }

          // 判断设备状态
          let deviceStatus = 'unknown';
          if (connection.connected) {
            if (statusData.error && statusData.error.value) {
              deviceStatus = 'error';
              this.triggerFlow('error');
            } else if (statusData.running && statusData.running.value) {
              deviceStatus = 'online';
              this.triggerFlow('online');
            } else {
              deviceStatus = 'offline';
              this.triggerFlow('offline');
            }
          } else {
            deviceStatus = 'disconnected';
            this.triggerFlow('offline');
          }

          this.setOutputValue('status', deviceStatus);
          this.setOutputValue('statusData', statusData);
          this.triggerFlow('statusChanged');

        } catch (error) {
          console.error('设备状态监控失败:', error);
          this.setOutputValue('status', 'error');
          this.triggerFlow('error');
        }
      }, interval * 1000);

    } else if (flowType === 'stop') {
      if (this.monitorTimer) {
        clearInterval(this.monitorTimer);
        this.monitorTimer = null;
      }
    }
  }

  destroy(): void {
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
      this.monitorTimer = null;
    }
    super.destroy();
  }
}

/**
 * PLC控制节点
 * 控制PLC设备的输入输出
 */
export class PLCControlNode extends VisualScriptNode {
  constructor() {
    super();
    this.title = 'PLC控制';
    this.category = NodeCategory.INDUSTRIAL;
    
    // 输入插槽
    this.addInputSlot('trigger', '触发', 'exec');
    this.addInputSlot('connection', 'PLC连接', 'object');
    this.addInputSlot('operation', '操作类型', 'string');
    this.addInputSlot('address', '地址', 'string');
    this.addInputSlot('value', '值', 'any');
    
    // 输出插槽
    this.addOutputSlot('success', '成功', 'exec');
    this.addOutputSlot('fail', '失败', 'exec');
    this.addOutputSlot('result', '结果', 'any');
  }

  async execute(): Promise<any> {
    const connection = this.getInputValue('connection') as any;
    const operation = this.getInputValue('operation') as string;
    const address = this.getInputValue('address') as string;
    const value = this.getInputValue('value');

    if (!connection || !operation || !address) {
      this.triggerFlow('fail');
      return null;
    }

    try {
      let result;
      
      switch (operation) {
        case 'read_coil':
          // 读取线圈状态
          result = await connection.readTag(`coil:${address}`);
          break;
          
        case 'write_coil':
          // 写入线圈状态
          result = await connection.writeTag(`coil:${address}`, Boolean(value));
          break;
          
        case 'read_register':
          // 读取寄存器
          result = await connection.readTag(`register:${address}`);
          break;
          
        case 'write_register':
          // 写入寄存器
          result = await connection.writeTag(`register:${address}`, Number(value));
          break;
          
        case 'read_input':
          // 读取输入状态
          result = await connection.readTag(`input:${address}`);
          break;
          
        default:
          throw new Error(`不支持的PLC操作: ${operation}`);
      }

      this.setOutputValue('result', result);
      this.triggerFlow('success');
      return result;
      
    } catch (error) {
      console.error('PLC控制失败:', error);
      this.triggerFlow('fail');
      return null;
    }
  }
}

/**
 * 传感器数据读取节点
 * 读取各种工业传感器数据
 */
export class SensorDataReadNode extends VisualScriptNode {
  constructor() {
    super();
    this.title = '传感器数据读取';
    this.category = NodeCategory.INDUSTRIAL;
    
    // 输入插槽
    this.addInputSlot('trigger', '触发', 'exec');
    this.addInputSlot('connection', '设备连接', 'object');
    this.addInputSlot('sensorType', '传感器类型', 'string');
    this.addInputSlot('sensorId', '传感器ID', 'string');
    this.addInputSlot('unit', '单位', 'string');
    
    // 输出插槽
    this.addOutputSlot('success', '成功', 'exec');
    this.addOutputSlot('fail', '失败', 'exec');
    this.addOutputSlot('value', '传感器值', 'number');
    this.addOutputSlot('unit', '单位', 'string');
    this.addOutputSlot('timestamp', '时间戳', 'string');
    this.addOutputSlot('status', '传感器状态', 'string');
  }

  async execute(): Promise<any> {
    const connection = this.getInputValue('connection') as any;
    const sensorType = this.getInputValue('sensorType') as string;
    const sensorId = this.getInputValue('sensorId') as string;
    const unit = this.getInputValue('unit') as string;

    if (!connection || !sensorType || !sensorId) {
      this.triggerFlow('fail');
      return null;
    }

    try {
      // 根据传感器类型生成模拟数据
      let value: number;
      let defaultUnit: string;
      
      switch (sensorType) {
        case 'temperature':
          value = 20 + Math.random() * 60; // 20-80°C
          defaultUnit = '°C';
          break;
          
        case 'pressure':
          value = Math.random() * 10; // 0-10 bar
          defaultUnit = 'bar';
          break;
          
        case 'vibration':
          value = Math.random() * 50; // 0-50 mm/s
          defaultUnit = 'mm/s';
          break;
          
        case 'flow':
          value = Math.random() * 100; // 0-100 L/min
          defaultUnit = 'L/min';
          break;
          
        case 'level':
          value = Math.random() * 100; // 0-100%
          defaultUnit = '%';
          break;
          
        case 'speed':
          value = Math.random() * 3000; // 0-3000 RPM
          defaultUnit = 'RPM';
          break;
          
        default:
          value = Math.random() * 100;
          defaultUnit = '';
      }

      const result = await connection.readTag(sensorId);
      const sensorValue = result?.value ?? value;
      const outputUnit = unit || defaultUnit;

      this.setOutputValue('value', sensorValue);
      this.setOutputValue('unit', outputUnit);
      this.setOutputValue('timestamp', new Date().toISOString());
      this.setOutputValue('status', 'normal');
      this.triggerFlow('success');
      
      return sensorValue;
      
    } catch (error) {
      console.error('传感器数据读取失败:', error);
      this.setOutputValue('status', 'error');
      this.triggerFlow('fail');
      return null;
    }
  }
}

/**
 * 工业报警节点
 * 处理工业设备的报警和警告
 */
export class IndustrialAlarmNode extends VisualScriptNode {
  constructor() {
    super();
    this.title = '工业报警';
    this.category = NodeCategory.INDUSTRIAL;
    
    // 输入插槽
    this.addInputSlot('trigger', '触发', 'exec');
    this.addInputSlot('value', '监控值', 'number');
    this.addInputSlot('highLimit', '高限值', 'number');
    this.addInputSlot('lowLimit', '低限值', 'number');
    this.addInputSlot('alarmType', '报警类型', 'string');
    this.addInputSlot('message', '报警消息', 'string');
    
    // 输出插槽
    this.addOutputSlot('normal', '正常', 'exec');
    this.addOutputSlot('warning', '警告', 'exec');
    this.addOutputSlot('alarm', '报警', 'exec');
    this.addOutputSlot('critical', '严重', 'exec');
    this.addOutputSlot('alarmData', '报警数据', 'object');
  }

  async execute(): Promise<any> {
    const value = this.getInputValue('value') as number;
    const highLimit = this.getInputValue('highLimit') as number;
    const lowLimit = this.getInputValue('lowLimit') as number;
    const alarmType = this.getInputValue('alarmType') as string || 'range';
    const message = this.getInputValue('message') as string || '数值超出范围';

    if (value === undefined) {
      return;
    }

    let alarmLevel = 'normal';
    let triggered = false;

    // 检查报警条件
    switch (alarmType) {
      case 'high':
        if (value > highLimit) {
          alarmLevel = value > highLimit * 1.2 ? 'critical' : 'alarm';
          triggered = true;
        } else if (value > highLimit * 0.9) {
          alarmLevel = 'warning';
          triggered = true;
        }
        break;
        
      case 'low':
        if (value < lowLimit) {
          alarmLevel = value < lowLimit * 0.8 ? 'critical' : 'alarm';
          triggered = true;
        } else if (value < lowLimit * 1.1) {
          alarmLevel = 'warning';
          triggered = true;
        }
        break;
        
      case 'range':
        if (value > highLimit || value < lowLimit) {
          alarmLevel = 'alarm';
          triggered = true;
        } else if (value > highLimit * 0.9 || value < lowLimit * 1.1) {
          alarmLevel = 'warning';
          triggered = true;
        }
        break;
    }

    const alarmData = {
      value,
      highLimit,
      lowLimit,
      alarmType,
      alarmLevel,
      message,
      timestamp: new Date().toISOString(),
      triggered
    };

    this.setOutputValue('alarmData', alarmData);
    this.triggerFlow(alarmLevel);

    if (triggered) {
      console.log(`工业报警触发: ${message} (值: ${value}, 级别: ${alarmLevel})`);
    }

    return alarmData;
  }
}

/**
 * 注册工业自动化节点
 * @param registry 节点注册表
 */
export function registerIndustrialAutomationNodes(registry: NodeRegistry): void {
  // 注册工业设备连接节点
  registry.registerNodeType({
    type: 'industrial/device/connect',
    category: NodeCategory.INDUSTRIAL,
    constructor: IndustrialDeviceConnectNode,
    label: '工业设备连接',
    description: '连接到工业设备并建立通信',
    icon: 'device-connect',
    color: '#FF6B35',
    tags: ['industrial', 'device', 'connect', 'communication']
  });

  // 注册读取设备标签节点
  registry.registerNodeType({
    type: 'industrial/device/readTag',
    category: NodeCategory.INDUSTRIAL,
    constructor: ReadDeviceTagNode,
    label: '读取设备标签',
    description: '从工业设备读取数据标签',
    icon: 'tag-read',
    color: '#FF6B35',
    tags: ['industrial', 'device', 'read', 'tag', 'data']
  });

  // 注册写入设备标签节点
  registry.registerNodeType({
    type: 'industrial/device/writeTag',
    category: NodeCategory.INDUSTRIAL,
    constructor: WriteDeviceTagNode,
    label: '写入设备标签',
    description: '向工业设备写入数据标签',
    icon: 'tag-write',
    color: '#FF6B35',
    tags: ['industrial', 'device', 'write', 'tag', 'control']
  });

  // 注册设备状态监控节点
  registry.registerNodeType({
    type: 'industrial/device/statusMonitor',
    category: NodeCategory.INDUSTRIAL,
    constructor: DeviceStatusMonitorNode,
    label: '设备状态监控',
    description: '监控工业设备的运行状态',
    icon: 'status-monitor',
    color: '#FF6B35',
    tags: ['industrial', 'device', 'monitor', 'status']
  });

  // 注册PLC控制节点
  registry.registerNodeType({
    type: 'industrial/plc/control',
    category: NodeCategory.INDUSTRIAL,
    constructor: PLCControlNode,
    label: 'PLC控制',
    description: '控制PLC设备的输入输出',
    icon: 'plc-control',
    color: '#FF6B35',
    tags: ['industrial', 'plc', 'control', 'automation']
  });

  // 注册传感器数据读取节点
  registry.registerNodeType({
    type: 'industrial/sensor/read',
    category: NodeCategory.INDUSTRIAL,
    constructor: SensorDataReadNode,
    label: '传感器数据读取',
    description: '读取各种工业传感器数据',
    icon: 'sensor-read',
    color: '#FF6B35',
    tags: ['industrial', 'sensor', 'read', 'data']
  });

  // 注册工业报警节点
  registry.registerNodeType({
    type: 'industrial/alarm',
    category: NodeCategory.INDUSTRIAL,
    constructor: IndustrialAlarmNode,
    label: '工业报警',
    description: '处理工业设备的报警和警告',
    icon: 'alarm',
    color: '#FF6B35',
    tags: ['industrial', 'alarm', 'warning', 'monitoring']
  });
}
