# 自然语言生成场景使用指南

## 概述

DL引擎的自然语言生成场景功能允许用户通过自然语言描述来快速创建3D场景。该功能集成了先进的自然语言处理技术和3D场景生成算法，为用户提供了直观、高效的场景创建体验。

## 功能特性

### 核心功能
- **自然语言理解**: 智能解析用户的场景描述
- **场景规划**: 基于理解结果自动规划场景布局
- **3D内容生成**: 自动生成场景中的3D对象和环境
- **实时预览**: 提供场景生成过程的实时预览
- **多风格支持**: 支持写实、卡通、简约、科幻、奇幻等多种风格

### 高级特性
- **情感分析**: 根据描述的情感倾向调整场景氛围
- **智能优化**: 自动优化场景性能和视觉效果
- **历史记录**: 保存和管理生成历史
- **模板系统**: 提供预设场景模板
- **视觉脚本集成**: 支持在视觉脚本中使用

## 使用方法

### 1. 编辑器界面使用

#### 基本操作
1. 打开编辑器，找到"自然语言场景生成"面板
2. 在文本输入框中描述您想要的场景
3. 选择生成风格和质量参数
4. 点击"生成场景"按钮

#### 示例描述
```
创建一个现代化的办公室，包含玻璃桌子、舒适的椅子和绿色植物
```

```
设计一个温馨的咖啡厅，有木质桌椅、暖色灯光和装饰画
```

```
建造一个科幻风格的实验室，有金属设备和蓝色光效
```

#### 参数配置
- **风格**: realistic(写实) | cartoon(卡通) | minimalist(简约) | scifi(科幻) | fantasy(奇幻)
- **质量**: 1-100，数值越高质量越好，生成时间越长
- **最大对象数**: 控制场景中对象的数量上限

### 2. 视觉脚本使用

#### 自然语言场景生成节点
```
节点类型: nlp/scene/generate
输入:
  - text: 场景描述文本
  - style: 生成风格 (可选)
  - quality: 质量等级 (可选)
  - maxObjects: 最大对象数 (可选)

输出:
  - scene: 生成的场景对象
  - understanding: 语言理解结果
  - progress: 生成进度
  - metadata: 生成元数据
```

#### 场景理解节点
```
节点类型: nlp/scene/understand
输入:
  - text: 输入文本

输出:
  - entities: 识别的实体
  - sentiment: 情感倾向
  - intent: 意图
  - keywords: 关键词
  - style: 推断风格
```

### 3. API使用

#### 生成场景
```http
POST /api/v1/nlp-scene/generate
Content-Type: application/json

{
  "text": "创建一个现代办公室",
  "style": "realistic",
  "quality": 80,
  "maxObjects": 50,
  "userId": "user123"
}
```

#### 预览场景
```http
POST /api/v1/nlp-scene/preview
Content-Type: application/json

{
  "text": "创建一个现代办公室",
  "style": "realistic",
  "lowQuality": true
}
```

#### 获取生成历史
```http
GET /api/v1/nlp-scene/history/user123?page=1&limit=10
```

## 最佳实践

### 描述技巧
1. **具体明确**: 使用具体的对象名称而不是模糊的描述
2. **包含风格**: 在描述中包含风格关键词
3. **情感表达**: 使用情感词汇来影响场景氛围
4. **空间关系**: 描述对象之间的位置关系

### 性能优化
1. **合理设置质量**: 根据需求选择合适的质量等级
2. **控制对象数量**: 避免设置过多的对象数量
3. **使用预览**: 先使用预览功能验证效果
4. **缓存利用**: 相同描述会使用缓存结果

### 错误处理
1. **检查输入**: 确保描述文本不为空
2. **网络超时**: 复杂场景可能需要较长生成时间
3. **资源限制**: 注意服务器资源限制

## 技术架构

### 引擎层
- `NLPSceneGenerator`: 核心生成系统
- 集成到DL引擎的系统架构中
- 支持事件监听和进度回调

### 编辑器层
- `NLPSceneGenerationPanel`: 主生成面板
- `ScenePreview`: 场景预览组件
- `GenerationHistory`: 历史记录组件

### 服务器层
- `NLPSceneService`: 场景生成服务
- `AIModelService`: AI模型服务
- `SceneStorageService`: 场景存储服务

### 视觉脚本层
- `NLPSceneGenerationNode`: 场景生成节点
- `SceneUnderstandingNode`: 场景理解节点

## 扩展开发

### 自定义风格
```typescript
// 在NLPSceneGenerator中添加新风格
private inferStyle(text: string): string {
  if (/工业|机械|钢铁/.test(text)) return 'industrial';
  // ... 其他风格判断
}
```

### 自定义对象类型
```typescript
// 扩展对象识别模式
const objectPatterns = [
  /桌子|椅子|沙发|床|柜子|书架/g,
  /新对象类型1|新对象类型2/g  // 添加新模式
];
```

### 集成外部AI服务
```typescript
// 在AIModelService中集成外部服务
async understandText(text: string): Promise<LanguageUnderstanding> {
  // 调用OpenAI、百度AI等外部服务
  const response = await this.httpService.post('external-ai-api', { text });
  return this.parseResponse(response.data);
}
```

## 故障排除

### 常见问题
1. **生成失败**: 检查网络连接和服务器状态
2. **质量不佳**: 调整质量参数或优化描述文本
3. **性能问题**: 降低质量等级或减少对象数量
4. **风格不匹配**: 在描述中明确指定风格关键词

### 调试方法
1. 查看浏览器控制台日志
2. 检查服务器端日志
3. 使用预览功能测试
4. 验证输入参数格式

## 更新日志

### v1.0.0 (2024-12-20)
- 初始版本发布
- 支持基本的自然语言场景生成
- 集成编辑器界面和视觉脚本
- 提供REST API接口

---

更多详细信息请参考技术文档或联系开发团队。
