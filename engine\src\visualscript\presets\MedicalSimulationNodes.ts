/**
 * MedicalSimulationNodes.ts
 * 
 * 医疗模拟节点 - 提供医疗展厅、健康教育、医疗设备交互等专业功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * 医疗知识查询节点
 */
export class MedicalKnowledgeQueryNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'medical/knowledge/query',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('query', SocketType.STRING, '', '查询内容');
    this.addInputSocket('category', SocketType.STRING, 'general', '医疗分类');
    this.addInputSocket('language', SocketType.STRING, 'zh-CN', '语言');
    this.addInputSocket('maxResults', SocketType.NUMBER, 5, '最大结果数');
    
    // 输出插槽
    this.addOutputSocket('results', SocketType.ARRAY, [], '查询结果');
    this.addOutputSocket('confidence', SocketType.NUMBER, 0, '置信度');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '查询成功');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const query = this.getInputValue('query') as string;
    const category = this.getInputValue('category') as string;
    const language = this.getInputValue('language') as string;
    const maxResults = this.getInputValue('maxResults') as number;

    if (!query.trim()) {
      this.setOutputValue('success', false);
      return { success: false, error: '查询内容不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const medicalService = world.getSystem('MedicalKnowledgeService');
      
      if (!medicalService) {
        this.setOutputValue('success', false);
        return { success: false, error: '医疗知识服务不可用' };
      }

      // 执行医疗知识查询
      const result = await medicalService.queryKnowledge({
        query,
        category,
        language,
        maxResults
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('results', result.results || []);
      this.setOutputValue('confidence', result.confidence || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 症状分析节点
 */
export class SymptomAnalysisNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'medical/analysis/symptoms',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('symptoms', SocketType.ARRAY, [], '症状列表');
    this.addInputSocket('patientAge', SocketType.NUMBER, 0, '患者年龄');
    this.addInputSocket('patientGender', SocketType.STRING, '', '患者性别');
    this.addInputSocket('medicalHistory', SocketType.ARRAY, [], '病史');
    
    // 输出插槽
    this.addOutputSocket('possibleConditions', SocketType.ARRAY, [], '可能疾病');
    this.addOutputSocket('recommendations', SocketType.ARRAY, [], '建议');
    this.addOutputSocket('urgencyLevel', SocketType.STRING, 'low', '紧急程度');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '分析成功');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const symptoms = this.getInputValue('symptoms') as string[];
    const patientAge = this.getInputValue('patientAge') as number;
    const patientGender = this.getInputValue('patientGender') as string;
    const medicalHistory = this.getInputValue('medicalHistory') as string[];

    if (!symptoms || symptoms.length === 0) {
      this.setOutputValue('success', false);
      return { success: false, error: '症状列表不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const medicalService = world.getSystem('MedicalAnalysisService');
      
      if (!medicalService) {
        this.setOutputValue('success', false);
        return { success: false, error: '医疗分析服务不可用' };
      }

      // 执行症状分析
      const result = await medicalService.analyzeSymptoms({
        symptoms,
        patientAge,
        patientGender,
        medicalHistory
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('possibleConditions', result.possibleConditions || []);
      this.setOutputValue('recommendations', result.recommendations || []);
      this.setOutputValue('urgencyLevel', result.urgencyLevel || 'low');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 医疗设备交互节点
 */
export class MedicalDeviceInteractionNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'medical/device/interaction',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('deviceId', SocketType.STRING, '', '设备ID');
    this.addInputSocket('action', SocketType.STRING, 'info', '操作类型');
    this.addInputSocket('parameters', SocketType.OBJECT, {}, '操作参数');
    this.addInputSocket('userRole', SocketType.STRING, 'visitor', '用户角色');
    
    // 输出插槽
    this.addOutputSocket('deviceInfo', SocketType.OBJECT, {}, '设备信息');
    this.addOutputSocket('operationResult', SocketType.OBJECT, {}, '操作结果');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '操作成功');
  }

  protected executeImpl(): any {
    const deviceId = this.getInputValue('deviceId') as string;
    const action = this.getInputValue('action') as string;
    const parameters = this.getInputValue('parameters') as any;
    const userRole = this.getInputValue('userRole') as string;

    if (!deviceId) {
      this.setOutputValue('success', false);
      return { success: false, error: '设备ID不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const deviceService = world.getSystem('MedicalDeviceService');
      
      if (!deviceService) {
        this.setOutputValue('success', false);
        return { success: false, error: '医疗设备服务不可用' };
      }

      // 执行设备交互
      const result = deviceService.interactWithDevice({
        deviceId,
        action,
        parameters,
        userRole
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('deviceInfo', result.deviceInfo || {});
      this.setOutputValue('operationResult', result.operationResult || {});
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 健康教育内容节点
 */
export class HealthEducationContentNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'medical/education/content',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('topic', SocketType.STRING, '', '教育主题');
    this.addInputSocket('targetAudience', SocketType.STRING, 'general', '目标受众');
    this.addInputSocket('contentType', SocketType.STRING, 'text', '内容类型');
    this.addInputSocket('difficulty', SocketType.STRING, 'basic', '难度级别');
    
    // 输出插槽
    this.addOutputSocket('content', SocketType.OBJECT, {}, '教育内容');
    this.addOutputSocket('mediaResources', SocketType.ARRAY, [], '媒体资源');
    this.addOutputSocket('interactiveElements', SocketType.ARRAY, [], '交互元素');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '获取成功');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const topic = this.getInputValue('topic') as string;
    const targetAudience = this.getInputValue('targetAudience') as string;
    const contentType = this.getInputValue('contentType') as string;
    const difficulty = this.getInputValue('difficulty') as string;

    if (!topic.trim()) {
      this.setOutputValue('success', false);
      return { success: false, error: '教育主题不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const educationService = world.getSystem('HealthEducationService');
      
      if (!educationService) {
        this.setOutputValue('success', false);
        return { success: false, error: '健康教育服务不可用' };
      }

      // 获取教育内容
      const result = await educationService.getEducationContent({
        topic,
        targetAudience,
        contentType,
        difficulty
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('content', result.content || {});
      this.setOutputValue('mediaResources', result.mediaResources || []);
      this.setOutputValue('interactiveElements', result.interactiveElements || []);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册医疗模拟节点
 */
export function registerMedicalSimulationNodes(registry: NodeRegistry): void {
  // 医疗知识查询节点
  registry.registerNodeType({
    type: 'medical/knowledge/query',
    category: NodeCategory.CUSTOM,
    constructor: MedicalKnowledgeQueryNode,
    label: '医疗知识查询',
    description: '查询医疗知识库信息',
    tags: ['medical', 'knowledge', 'query'],
    version: '1.0.0'
  });

  // 症状分析节点
  registry.registerNodeType({
    type: 'medical/analysis/symptoms',
    category: NodeCategory.CUSTOM,
    constructor: SymptomAnalysisNode,
    label: '症状分析',
    description: '分析症状并提供建议',
    tags: ['medical', 'analysis', 'symptoms'],
    version: '1.0.0'
  });

  // 医疗设备交互节点
  registry.registerNodeType({
    type: 'medical/device/interaction',
    category: NodeCategory.CUSTOM,
    constructor: MedicalDeviceInteractionNode,
    label: '医疗设备交互',
    description: '与医疗设备进行交互',
    tags: ['medical', 'device', 'interaction'],
    version: '1.0.0'
  });

  // 健康教育内容节点
  registry.registerNodeType({
    type: 'medical/education/content',
    category: NodeCategory.CUSTOM,
    constructor: HealthEducationContentNode,
    label: '健康教育内容',
    description: '获取健康教育内容',
    tags: ['medical', 'education', 'content'],
    version: '1.0.0'
  });
}
