import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as moment from 'moment';
import * as ss from 'simple-statistics';
import { SLR } from 'ml-regression';

/**
 * 生产数据接口
 */
interface ProductionData {
  timestamp: Date;
  deviceId: string;
  deviceType: string;
  productionLine: string;
  output: number;
  quality: number;
  efficiency: number;
  downtime: number;
  energyConsumption: number;
  temperature: number;
  pressure: number;
  vibration: number;
}

/**
 * 分析结果接口
 */
interface AnalysisResult {
  type: string;
  title: string;
  description: string;
  data: any;
  insights: string[];
  recommendations: string[];
  confidence: number;
  timestamp: Date;
}

/**
 * KPI指标接口
 */
interface KPIMetrics {
  oee: number;
  availability: number;
  performance: number;
  quality: number;
  throughput: number;
  cycleTime: number;
  downtime: number;
  energyEfficiency: number;
  defectRate: number;
  onTimeDelivery: number;
}

/**
 * 趋势分析结果接口
 */
interface TrendAnalysis {
  metric: string;
  trend: 'increasing' | 'decreasing' | 'stable';
  slope: number;
  correlation: number;
  forecast: number[];
  confidence: number;
}

/**
 * 异常检测结果接口
 */
interface AnomalyDetection {
  deviceId: string;
  metric: string;
  value: number;
  threshold: number;
  severity: 'low' | 'medium' | 'high';
  timestamp: Date;
  description: string;
}

/**
 * 高级分析服务
 */
@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);
  
  // 缓存分析结果
  private analysisCache: Map<string, AnalysisResult> = new Map();
  private kpiCache: Map<string, KPIMetrics> = new Map();

  constructor() {
    // 启动定时分析任务
    this.startPeriodicAnalysis();
  }

  /**
   * 计算实时KPI指标
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @param deviceIds 设备ID列表
   * @returns KPI指标
   */
  async calculateKPIMetrics(
    startDate: Date, 
    endDate: Date, 
    deviceIds?: string[]
  ): Promise<KPIMetrics> {
    try {
      // 模拟获取生产数据
      const productionData = await this.getProductionData(startDate, endDate, deviceIds);
      
      if (productionData.length === 0) {
        return this.getDefaultKPIMetrics();
      }

      // 计算各项KPI指标
      const availability = this.calculateAvailability(productionData);
      const performance = this.calculatePerformance(productionData);
      const quality = this.calculateQuality(productionData);
      const oee = (availability * performance * quality) / 10000; // 转换为百分比

      const kpiMetrics: KPIMetrics = {
        oee: Math.round(oee * 100) / 100,
        availability: Math.round(availability * 100) / 100,
        performance: Math.round(performance * 100) / 100,
        quality: Math.round(quality * 100) / 100,
        throughput: this.calculateThroughput(productionData),
        cycleTime: this.calculateAverageCycleTime(productionData),
        downtime: this.calculateTotalDowntime(productionData),
        energyEfficiency: this.calculateEnergyEfficiency(productionData),
        defectRate: this.calculateDefectRate(productionData),
        onTimeDelivery: this.calculateOnTimeDelivery(productionData)
      };

      // 缓存结果
      const cacheKey = `kpi_${startDate.getTime()}_${endDate.getTime()}_${deviceIds?.join(',') || 'all'}`;
      this.kpiCache.set(cacheKey, kpiMetrics);

      this.logger.log(`KPI指标计算完成: OEE=${kpiMetrics.oee}%`);
      return kpiMetrics;

    } catch (error) {
      this.logger.error('计算KPI指标失败', error);
      return this.getDefaultKPIMetrics();
    }
  }

  /**
   * 执行趋势分析
   * @param metric 指标名称
   * @param period 分析周期（天）
   * @param deviceIds 设备ID列表
   * @returns 趋势分析结果
   */
  async performTrendAnalysis(
    metric: string, 
    period: number = 30, 
    deviceIds?: string[]
  ): Promise<TrendAnalysis> {
    try {
      const endDate = new Date();
      const startDate = moment(endDate).subtract(period, 'days').toDate();
      
      const productionData = await this.getProductionData(startDate, endDate, deviceIds);
      
      // 提取指标数据
      const metricData = this.extractMetricData(productionData, metric);
      
      if (metricData.length < 2) {
        throw new Error('数据不足，无法进行趋势分析');
      }

      // 计算趋势
      const xValues = metricData.map((_, index) => index);
      const yValues = metricData.map(d => d.value);
      
      // 线性回归分析
      const regression = new SLR(xValues, yValues);
      const slope = regression.slope;
      const correlation = ss.sampleCorrelation(xValues, yValues);
      
      // 判断趋势方向
      let trend: 'increasing' | 'decreasing' | 'stable';
      if (Math.abs(slope) < 0.01) {
        trend = 'stable';
      } else if (slope > 0) {
        trend = 'increasing';
      } else {
        trend = 'decreasing';
      }

      // 预测未来7天的值
      const forecast = [];
      for (let i = 1; i <= 7; i++) {
        const predictedValue = regression.predict(metricData.length + i);
        forecast.push(Math.max(0, predictedValue)); // 确保预测值非负
      }

      // 计算置信度
      const confidence = Math.min(Math.abs(correlation) * 100, 95);

      const trendAnalysis: TrendAnalysis = {
        metric,
        trend,
        slope,
        correlation,
        forecast,
        confidence
      };

      this.logger.log(`趋势分析完成: ${metric} - ${trend} (置信度: ${confidence.toFixed(1)}%)`);
      return trendAnalysis;

    } catch (error) {
      this.logger.error(`趋势分析失败: ${metric}`, error);
      throw error;
    }
  }

  /**
   * 异常检测
   * @param deviceIds 设备ID列表
   * @param sensitivity 敏感度 (0-1)
   * @returns 异常检测结果
   */
  async detectAnomalies(deviceIds?: string[], sensitivity: number = 0.8): Promise<AnomalyDetection[]> {
    try {
      const endDate = new Date();
      const startDate = moment(endDate).subtract(24, 'hours').toDate();
      
      const productionData = await this.getProductionData(startDate, endDate, deviceIds);
      const anomalies: AnomalyDetection[] = [];

      // 按设备分组数据
      const deviceDataMap = new Map<string, ProductionData[]>();
      productionData.forEach(data => {
        if (!deviceDataMap.has(data.deviceId)) {
          deviceDataMap.set(data.deviceId, []);
        }
        deviceDataMap.get(data.deviceId)!.push(data);
      });

      // 对每个设备进行异常检测
      for (const [deviceId, deviceData] of deviceDataMap) {
        const deviceAnomalies = await this.detectDeviceAnomalies(deviceId, deviceData, sensitivity);
        anomalies.push(...deviceAnomalies);
      }

      this.logger.log(`异常检测完成: 发现 ${anomalies.length} 个异常`);
      return anomalies;

    } catch (error) {
      this.logger.error('异常检测失败', error);
      return [];
    }
  }

  /**
   * 生成生产报告
   * @param reportType 报告类型
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @param options 报告选项
   * @returns 报告数据
   */
  async generateProductionReport(
    reportType: 'daily' | 'weekly' | 'monthly',
    startDate: Date,
    endDate: Date,
    options?: any
  ): Promise<any> {
    try {
      const productionData = await this.getProductionData(startDate, endDate);
      const kpiMetrics = await this.calculateKPIMetrics(startDate, endDate);
      
      const report = {
        reportType,
        period: {
          startDate,
          endDate,
          duration: moment(endDate).diff(moment(startDate), 'days') + 1
        },
        summary: {
          totalOutput: productionData.reduce((sum, d) => sum + d.output, 0),
          averageQuality: ss.mean(productionData.map(d => d.quality)),
          totalDowntime: productionData.reduce((sum, d) => sum + d.downtime, 0),
          energyConsumption: productionData.reduce((sum, d) => sum + d.energyConsumption, 0)
        },
        kpiMetrics,
        trends: await this.generateTrendSummary(productionData),
        topIssues: await this.identifyTopIssues(productionData),
        recommendations: await this.generateRecommendations(kpiMetrics, productionData),
        charts: await this.generateChartData(productionData, reportType),
        timestamp: new Date()
      };

      this.logger.log(`生产报告生成完成: ${reportType} (${startDate.toISOString().split('T')[0]} - ${endDate.toISOString().split('T')[0]})`);
      return report;

    } catch (error) {
      this.logger.error('生成生产报告失败', error);
      throw error;
    }
  }

  /**
   * 预测性分析
   * @param metric 预测指标
   * @param horizon 预测时间范围（天）
   * @param deviceIds 设备ID列表
   * @returns 预测结果
   */
  async performPredictiveAnalysis(
    metric: string,
    horizon: number = 7,
    deviceIds?: string[]
  ): Promise<any> {
    try {
      // 获取历史数据（过去30天）
      const endDate = new Date();
      const startDate = moment(endDate).subtract(30, 'days').toDate();
      
      const productionData = await this.getProductionData(startDate, endDate, deviceIds);
      const metricData = this.extractMetricData(productionData, metric);

      if (metricData.length < 10) {
        throw new Error('历史数据不足，无法进行预测分析');
      }

      // 时间序列分析
      const values = metricData.map(d => d.value);
      const timestamps = metricData.map(d => d.timestamp.getTime());

      // 简单的移动平均预测
      const windowSize = Math.min(7, Math.floor(values.length / 3));
      const predictions = [];
      
      for (let i = 0; i < horizon; i++) {
        const recentValues = values.slice(-windowSize);
        const prediction = ss.mean(recentValues);
        predictions.push({
          date: moment(endDate).add(i + 1, 'days').toDate(),
          value: prediction,
          confidence: Math.max(0, 90 - i * 5) // 置信度随时间递减
        });
      }

      // 计算预测准确性指标
      const accuracy = this.calculatePredictionAccuracy(values);

      const result = {
        metric,
        horizon,
        predictions,
        accuracy,
        methodology: 'Moving Average',
        dataPoints: metricData.length,
        timestamp: new Date()
      };

      this.logger.log(`预测性分析完成: ${metric} - ${horizon}天预测`);
      return result;

    } catch (error) {
      this.logger.error(`预测性分析失败: ${metric}`, error);
      throw error;
    }
  }

  /**
   * 根因分析
   * @param issue 问题描述
   * @param timeRange 时间范围
   * @param deviceIds 相关设备
   * @returns 根因分析结果
   */
  async performRootCauseAnalysis(
    issue: string,
    timeRange: { start: Date; end: Date },
    deviceIds?: string[]
  ): Promise<any> {
    try {
      const productionData = await this.getProductionData(timeRange.start, timeRange.end, deviceIds);
      
      // 分析相关因素
      const correlationAnalysis = await this.analyzeCorrelations(productionData);
      const anomalyPatterns = await this.identifyAnomalyPatterns(productionData);
      const timelineAnalysis = await this.analyzeTimeline(productionData, issue);

      const rootCauseAnalysis = {
        issue,
        timeRange,
        potentialCauses: [
          ...correlationAnalysis.strongCorrelations,
          ...anomalyPatterns.significantPatterns
        ],
        timeline: timelineAnalysis,
        recommendations: this.generateRootCauseRecommendations(correlationAnalysis, anomalyPatterns),
        confidence: this.calculateRootCauseConfidence(correlationAnalysis, anomalyPatterns),
        timestamp: new Date()
      };

      this.logger.log(`根因分析完成: ${issue}`);
      return rootCauseAnalysis;

    } catch (error) {
      this.logger.error(`根因分析失败: ${issue}`, error);
      throw error;
    }
  }

  /**
   * 启动定期分析任务
   */
  private startPeriodicAnalysis(): void {
    // 每小时执行一次实时分析
    setInterval(async () => {
      await this.performRealTimeAnalysis();
    }, 60 * 60 * 1000);

    this.logger.log('定期分析任务已启动');
  }

  /**
   * 执行实时分析
   */
  @Cron(CronExpression.EVERY_HOUR)
  private async performRealTimeAnalysis(): Promise<void> {
    try {
      const endDate = new Date();
      const startDate = moment(endDate).subtract(1, 'hour').toDate();

      // 计算实时KPI
      const kpiMetrics = await this.calculateKPIMetrics(startDate, endDate);
      
      // 异常检测
      const anomalies = await this.detectAnomalies(undefined, 0.9);
      
      // 如果发现严重异常，发送警报
      const criticalAnomalies = anomalies.filter(a => a.severity === 'high');
      if (criticalAnomalies.length > 0) {
        this.logger.warn(`发现 ${criticalAnomalies.length} 个严重异常`);
        // 这里可以发送通知
      }

      this.logger.debug('实时分析完成');

    } catch (error) {
      this.logger.error('实时分析失败', error);
    }
  }

  // 私有辅助方法
  private async getProductionData(startDate: Date, endDate: Date, deviceIds?: string[]): Promise<ProductionData[]> {
    // 模拟生产数据
    const data: ProductionData[] = [];
    const devices = deviceIds || ['device-001', 'device-002', 'device-003'];
    
    let current = moment(startDate);
    while (current.isBefore(endDate)) {
      devices.forEach(deviceId => {
        data.push({
          timestamp: current.toDate(),
          deviceId,
          deviceType: 'CNC_MACHINE',
          productionLine: 'LINE_001',
          output: 80 + Math.random() * 40,
          quality: 95 + Math.random() * 5,
          efficiency: 85 + Math.random() * 15,
          downtime: Math.random() * 30,
          energyConsumption: 50 + Math.random() * 20,
          temperature: 25 + Math.random() * 10,
          pressure: 100 + Math.random() * 20,
          vibration: Math.random() * 5
        });
      });
      current.add(1, 'hour');
    }
    
    return data;
  }

  private calculateAvailability(data: ProductionData[]): number {
    const totalTime = data.length;
    const downtime = data.reduce((sum, d) => sum + d.downtime, 0);
    return totalTime > 0 ? ((totalTime * 60 - downtime) / (totalTime * 60)) * 100 : 0;
  }

  private calculatePerformance(data: ProductionData[]): number {
    const avgEfficiency = ss.mean(data.map(d => d.efficiency));
    return avgEfficiency;
  }

  private calculateQuality(data: ProductionData[]): number {
    const avgQuality = ss.mean(data.map(d => d.quality));
    return avgQuality;
  }

  private calculateThroughput(data: ProductionData[]): number {
    return data.reduce((sum, d) => sum + d.output, 0);
  }

  private calculateAverageCycleTime(data: ProductionData[]): number {
    // 简化计算
    return 60 / ss.mean(data.map(d => d.efficiency / 100));
  }

  private calculateTotalDowntime(data: ProductionData[]): number {
    return data.reduce((sum, d) => sum + d.downtime, 0);
  }

  private calculateEnergyEfficiency(data: ProductionData[]): number {
    const totalOutput = this.calculateThroughput(data);
    const totalEnergy = data.reduce((sum, d) => sum + d.energyConsumption, 0);
    return totalEnergy > 0 ? totalOutput / totalEnergy : 0;
  }

  private calculateDefectRate(data: ProductionData[]): number {
    const avgQuality = this.calculateQuality(data);
    return 100 - avgQuality;
  }

  private calculateOnTimeDelivery(data: ProductionData[]): number {
    // 简化计算，基于效率
    const avgEfficiency = this.calculatePerformance(data);
    return Math.min(avgEfficiency, 100);
  }

  private getDefaultKPIMetrics(): KPIMetrics {
    return {
      oee: 0,
      availability: 0,
      performance: 0,
      quality: 0,
      throughput: 0,
      cycleTime: 0,
      downtime: 0,
      energyEfficiency: 0,
      defectRate: 0,
      onTimeDelivery: 0
    };
  }

  private extractMetricData(data: ProductionData[], metric: string): { timestamp: Date; value: number }[] {
    return data.map(d => ({
      timestamp: d.timestamp,
      value: (d as any)[metric] || 0
    }));
  }

  private async detectDeviceAnomalies(deviceId: string, data: ProductionData[], sensitivity: number): Promise<AnomalyDetection[]> {
    const anomalies: AnomalyDetection[] = [];
    const metrics = ['output', 'quality', 'efficiency', 'temperature', 'pressure', 'vibration'];

    for (const metric of metrics) {
      const values = data.map(d => (d as any)[metric]);
      const mean = ss.mean(values);
      const stdDev = ss.standardDeviation(values);
      const threshold = mean + (sensitivity * 2 * stdDev);

      const latestValue = values[values.length - 1];
      if (latestValue > threshold) {
        anomalies.push({
          deviceId,
          metric,
          value: latestValue,
          threshold,
          severity: latestValue > threshold * 1.5 ? 'high' : 'medium',
          timestamp: data[data.length - 1].timestamp,
          description: `${metric} 值异常偏高: ${latestValue.toFixed(2)} (阈值: ${threshold.toFixed(2)})`
        });
      }
    }

    return anomalies;
  }

  private async generateTrendSummary(data: ProductionData[]): Promise<any> {
    // 简化实现
    return {
      output: 'increasing',
      quality: 'stable',
      efficiency: 'decreasing'
    };
  }

  private async identifyTopIssues(data: ProductionData[]): Promise<any[]> {
    // 简化实现
    return [
      { issue: '设备效率下降', frequency: 15, impact: 'high' },
      { issue: '质量波动', frequency: 8, impact: 'medium' },
      { issue: '能耗偏高', frequency: 12, impact: 'medium' }
    ];
  }

  private async generateRecommendations(kpi: KPIMetrics, data: ProductionData[]): Promise<string[]> {
    const recommendations: string[] = [];

    if (kpi.oee < 70) {
      recommendations.push('OEE偏低，建议检查设备维护计划');
    }

    if (kpi.quality < 95) {
      recommendations.push('质量指标需要改善，建议加强质量控制');
    }

    if (kpi.energyEfficiency < 2) {
      recommendations.push('能耗效率偏低，建议优化设备运行参数');
    }

    return recommendations;
  }

  private async generateChartData(data: ProductionData[], reportType: string): Promise<any> {
    // 简化实现
    return {
      oeeChart: data.map(d => ({ x: d.timestamp, y: d.efficiency })),
      qualityChart: data.map(d => ({ x: d.timestamp, y: d.quality })),
      outputChart: data.map(d => ({ x: d.timestamp, y: d.output }))
    };
  }

  private calculatePredictionAccuracy(values: number[]): number {
    // 简化的准确性计算
    const variance = ss.variance(values);
    const mean = ss.mean(values);
    const cv = variance / mean;
    return Math.max(0, 100 - cv * 100);
  }

  private async analyzeCorrelations(data: ProductionData[]): Promise<any> {
    // 简化实现
    return {
      strongCorrelations: [
        { factor: 'temperature', correlation: 0.8, impact: 'negative' },
        { factor: 'maintenance', correlation: 0.7, impact: 'positive' }
      ]
    };
  }

  private async identifyAnomalyPatterns(data: ProductionData[]): Promise<any> {
    // 简化实现
    return {
      significantPatterns: [
        { pattern: 'temperature_spike', frequency: 5, severity: 'high' },
        { pattern: 'efficiency_drop', frequency: 3, severity: 'medium' }
      ]
    };
  }

  private async analyzeTimeline(data: ProductionData[], issue: string): Promise<any> {
    // 简化实现
    return {
      events: [
        { timestamp: new Date(), event: 'issue_detected', description: issue },
        { timestamp: new Date(), event: 'parameter_change', description: '温度参数异常' }
      ]
    };
  }

  private generateRootCauseRecommendations(correlations: any, patterns: any): string[] {
    return [
      '检查设备温度控制系统',
      '优化维护计划',
      '调整工艺参数'
    ];
  }

  private calculateRootCauseConfidence(correlations: any, patterns: any): number {
    return 75; // 简化实现
  }
}
