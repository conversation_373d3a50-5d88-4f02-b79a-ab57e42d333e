/**
 * 自然语言处理节点
 */
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';
import { NLPSceneGenerationNode } from '../nodes/NLPSceneGenerationNode';
import { SceneUnderstandingNode } from '../nodes/SceneUnderstandingNode';

/**
 * 注册自然语言处理相关节点
 */
export function registerNLPNodes(registry: NodeRegistry): void {
  // 注册自然语言场景生成节点
  registry.registerNodeType({
    type: 'nlp/scene/generate',
    category: NodeCategory.AI,
    constructor: NLPSceneGenerationNode,
    label: '自然语言场景生成',
    description: '基于自然语言描述生成3D场景',
    icon: 'robot',
    color: '#722ED1',
    tags: ['nlp', 'scene', 'generation', 'ai'],
    version: '1.0.0',
    author: 'DL Engine Team'
  });

  // 注册场景理解节点
  registry.registerNodeType({
    type: 'nlp/scene/understand',
    category: NodeCategory.AI,
    constructor: SceneUnderstandingNode,
    label: '场景理解',
    description: '理解自然语言中的场景描述',
    icon: 'eye',
    color: '#52C41A',
    tags: ['nlp', 'understanding', 'analysis'],
    version: '1.0.0',
    author: 'DL Engine Team'
  });

  console.log('已注册自然语言处理节点');
}

/**
 * 获取NLP节点库信息
 */
export function getNLPNodeLibrary(): any {
  return {
    name: '自然语言处理',
    description: '提供自然语言理解和场景生成功能',
    version: '1.0.0',
    nodes: [
      {
        type: 'nlp/scene/generate',
        name: '自然语言场景生成',
        category: 'AI',
        complexity: 'advanced',
        performance: 'heavy'
      },
      {
        type: 'nlp/scene/understand',
        name: '场景理解',
        category: 'AI',
        complexity: 'intermediate',
        performance: 'medium'
      }
    ]
  };
}
