import { 
  IndustrialProtocol, 
  ProtocolType, 
  DeviceConfig, 
  DeviceConnection, 
  IndustrialDataPoint,
  DeviceStatus,
  DataQuality 
} from '../types';
import { Debug } from '../../core/Debug';

/**
 * MQTT协议实现
 * 支持MQTT发布/订阅模式的工业物联网通信
 */
export class MQTTProtocol implements IndustrialProtocol {
  public readonly type = ProtocolType.MQTT;
  
  private connections: Map<string, MQTTConnection> = new Map();
  private subscriptions: Map<string, MQTTSubscription> = new Map();
  private subscriptionCounter = 0;

  /**
   * 连接设备
   * @param config 设备配置
   * @returns 设备连接信息
   */
  public async connect(config: DeviceConfig): Promise<DeviceConnection> {
    try {
      Debug.log('MQTTProtocol', `正在连接MQTT代理: ${config.name}`);
      
      // 创建MQTT连接
      const mqttConnection = await this.createMQTTConnection(config);
      
      const connection: DeviceConnection = {
        deviceId: config.id,
        protocol: this.type,
        status: DeviceStatus.ONLINE,
        lastConnected: new Date(),
        errorCount: 0
      };
      
      this.connections.set(config.id, mqttConnection);
      
      Debug.log('MQTTProtocol', `MQTT代理连接成功: ${config.name}`);
      return connection;
      
    } catch (error) {
      Debug.error('MQTTProtocol', `MQTT代理连接失败: ${config.name}`, error);
      throw error;
    }
  }

  /**
   * 断开设备连接
   * @param deviceId 设备ID
   */
  public async disconnect(deviceId: string): Promise<void> {
    const connection = this.connections.get(deviceId);
    if (connection) {
      try {
        await connection.disconnect();
        this.connections.delete(deviceId);
        Debug.log('MQTTProtocol', `MQTT代理断开连接: ${deviceId}`);
      } catch (error) {
        Debug.error('MQTTProtocol', `断开MQTT代理连接失败: ${deviceId}`, error);
      }
    }
  }

  /**
   * 读取标签数据
   * @param deviceId 设备ID
   * @param tagId 标签ID（MQTT主题）
   * @returns 工业数据点
   */
  public async readTag(deviceId: string, tagId: string): Promise<IndustrialDataPoint> {
    const connection = this.connections.get(deviceId);
    if (!connection) {
      throw new Error(`MQTT代理未连接: ${deviceId}`);
    }

    try {
      // MQTT是发布/订阅模式，没有直接的读取操作
      // 这里返回缓存的最新值
      const cachedValue = connection.getLastValue(tagId);
      
      if (cachedValue) {
        return {
          tagId,
          deviceId,
          timestamp: cachedValue.timestamp,
          value: cachedValue.value,
          quality: DataQuality.GOOD,
          metadata: { topic: tagId }
        };
      } else {
        return {
          tagId,
          deviceId,
          timestamp: new Date(),
          value: null,
          quality: DataQuality.STALE,
          metadata: { topic: tagId, error: '无缓存数据' }
        };
      }
      
    } catch (error) {
      Debug.error('MQTTProtocol', `读取MQTT主题失败: ${deviceId}:${tagId}`, error);
      
      return {
        tagId,
        deviceId,
        timestamp: new Date(),
        value: null,
        quality: DataQuality.BAD,
        metadata: { topic: tagId, error: error.message }
      };
    }
  }

  /**
   * 写入标签数据
   * @param deviceId 设备ID
   * @param tagId 标签ID（MQTT主题）
   * @param value 写入值
   * @returns 是否写入成功
   */
  public async writeTag(deviceId: string, tagId: string, value: any): Promise<boolean> {
    const connection = this.connections.get(deviceId);
    if (!connection) {
      throw new Error(`MQTT代理未连接: ${deviceId}`);
    }

    try {
      // 发布MQTT消息
      await connection.publish(tagId, value);
      
      Debug.log('MQTTProtocol', `发布MQTT消息成功: ${deviceId}:${tagId} = ${value}`);
      return true;
      
    } catch (error) {
      Debug.error('MQTTProtocol', `发布MQTT消息失败: ${deviceId}:${tagId}`, error);
      return false;
    }
  }

  /**
   * 批量读取标签
   * @param deviceId 设备ID
   * @param tagIds 标签ID列表（MQTT主题列表）
   * @returns 工业数据点数组
   */
  public async readMultipleTags(deviceId: string, tagIds: string[]): Promise<IndustrialDataPoint[]> {
    const results: IndustrialDataPoint[] = [];
    
    for (const tagId of tagIds) {
      try {
        const dataPoint = await this.readTag(deviceId, tagId);
        results.push(dataPoint);
      } catch (error) {
        results.push({
          tagId,
          deviceId,
          timestamp: new Date(),
          value: null,
          quality: DataQuality.BAD,
          metadata: { topic: tagId, error: error.message }
        });
      }
    }
    
    return results;
  }

  /**
   * 订阅数据
   * @param deviceId 设备ID
   * @param tagIds 标签ID列表（MQTT主题列表）
   * @param callback 数据回调函数
   * @returns 订阅ID
   */
  public async subscribe(
    deviceId: string, 
    tagIds: string[], 
    callback: (data: IndustrialDataPoint[]) => void
  ): Promise<string> {
    const connection = this.connections.get(deviceId);
    if (!connection) {
      throw new Error(`MQTT代理未连接: ${deviceId}`);
    }

    const subscriptionId = `mqtt_sub_${++this.subscriptionCounter}`;
    
    try {
      // 订阅MQTT主题
      await connection.subscribeTopics(tagIds, (topic, message) => {
        const dataPoint: IndustrialDataPoint = {
          tagId: topic,
          deviceId,
          timestamp: new Date(),
          value: this.parseMessage(message),
          quality: DataQuality.GOOD,
          metadata: { topic }
        };
        
        callback([dataPoint]);
      });
      
      const subscription: MQTTSubscription = {
        id: subscriptionId,
        deviceId,
        tagIds,
        callback
      };
      
      this.subscriptions.set(subscriptionId, subscription);
      
      Debug.log('MQTTProtocol', `MQTT数据订阅创建: ${subscriptionId}`);
      return subscriptionId;
      
    } catch (error) {
      Debug.error('MQTTProtocol', `创建MQTT订阅失败: ${deviceId}`, error);
      throw error;
    }
  }

  /**
   * 取消订阅
   * @param subscriptionId 订阅ID
   */
  public async unsubscribe(subscriptionId: string): Promise<void> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      const connection = this.connections.get(subscription.deviceId);
      if (connection) {
        try {
          await connection.unsubscribeTopics(subscription.tagIds);
        } catch (error) {
          Debug.error('MQTTProtocol', `取消MQTT主题订阅失败: ${subscriptionId}`, error);
        }
      }
      
      this.subscriptions.delete(subscriptionId);
      Debug.log('MQTTProtocol', `MQTT数据订阅取消: ${subscriptionId}`);
    }
  }

  /**
   * 创建MQTT连接
   * @param config 设备配置
   * @returns MQTT连接
   */
  private async createMQTTConnection(config: DeviceConfig): Promise<MQTTConnection> {
    // 在浏览器环境中，我们模拟MQTT连接
    // 实际实现中，这里会使用真实的MQTT客户端库（如mqtt.js）
    
    const brokerUrl = `mqtt://${config.address}:${config.port || 1883}`;
    
    const connection: MQTTConnection = {
      deviceId: config.id,
      brokerUrl,
      connected: true,
      lastValues: new Map(),
      subscriptions: new Map(),
      
      async publish(topic: string, message: any): Promise<void> {
        // 模拟MQTT发布
        Debug.log('MQTTProtocol', `模拟发布MQTT消息: ${topic} = ${JSON.stringify(message)}`);
        
        // 模拟发布延迟
        await new Promise(resolve => setTimeout(resolve, 10));
      },
      
      async subscribeTopics(topics: string[], callback: (topic: string, message: any) => void): Promise<void> {
        // 模拟MQTT订阅
        for (const topic of topics) {
          if (!this.subscriptions.has(topic)) {
            // 创建模拟数据生成器
            const timer = setInterval(() => {
              const mockMessage = this.generateMockMessage(topic);
              this.lastValues.set(topic, {
                value: mockMessage,
                timestamp: new Date()
              });
              callback(topic, mockMessage);
            }, 2000 + Math.random() * 3000); // 2-5秒随机间隔
            
            this.subscriptions.set(topic, timer);
          }
        }
        
        Debug.log('MQTTProtocol', `模拟订阅MQTT主题: ${topics.join(', ')}`);
      },
      
      async unsubscribeTopics(topics: string[]): Promise<void> {
        // 模拟取消MQTT订阅
        for (const topic of topics) {
          const timer = this.subscriptions.get(topic);
          if (timer) {
            clearInterval(timer);
            this.subscriptions.delete(topic);
          }
        }
        
        Debug.log('MQTTProtocol', `模拟取消订阅MQTT主题: ${topics.join(', ')}`);
      },
      
      getLastValue(topic: string): { value: any; timestamp: Date } | undefined {
        return this.lastValues.get(topic);
      },
      
      async disconnect(): Promise<void> {
        // 清理所有订阅
        this.subscriptions.forEach(timer => clearInterval(timer));
        this.subscriptions.clear();
        this.lastValues.clear();
        this.connected = false;
        
        Debug.log('MQTTProtocol', `MQTT连接已断开: ${this.deviceId}`);
      },
      
      generateMockMessage(topic: string): any {
        // 根据主题生成模拟数据
        if (topic.includes('temperature')) {
          return {
            value: 20 + Math.random() * 60,
            unit: '°C',
            timestamp: new Date().toISOString()
          };
        } else if (topic.includes('pressure')) {
          return {
            value: Math.random() * 10,
            unit: 'bar',
            timestamp: new Date().toISOString()
          };
        } else if (topic.includes('status')) {
          return {
            value: Math.random() > 0.8 ? 'running' : 'stopped',
            timestamp: new Date().toISOString()
          };
        } else if (topic.includes('count')) {
          return {
            value: Math.floor(Math.random() * 1000),
            timestamp: new Date().toISOString()
          };
        } else {
          return {
            value: Math.random() * 100,
            timestamp: new Date().toISOString()
          };
        }
      }
    };
    
    // 模拟连接延迟
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return connection;
  }

  /**
   * 解析MQTT消息
   * @param message 原始消息
   * @returns 解析后的值
   */
  private parseMessage(message: any): any {
    if (typeof message === 'string') {
      try {
        return JSON.parse(message);
      } catch {
        return message;
      }
    }
    return message;
  }
}

/**
 * MQTT连接接口
 */
interface MQTTConnection {
  deviceId: string;
  brokerUrl: string;
  connected: boolean;
  lastValues: Map<string, { value: any; timestamp: Date }>;
  subscriptions: Map<string, NodeJS.Timeout>;
  publish(topic: string, message: any): Promise<void>;
  subscribeTopics(topics: string[], callback: (topic: string, message: any) => void): Promise<void>;
  unsubscribeTopics(topics: string[]): Promise<void>;
  getLastValue(topic: string): { value: any; timestamp: Date } | undefined;
  disconnect(): Promise<void>;
  generateMockMessage(topic: string): any;
}

/**
 * MQTT订阅
 */
interface MQTTSubscription {
  id: string;
  deviceId: string;
  tagIds: string[];
  callback: (data: IndustrialDataPoint[]) => void;
}
