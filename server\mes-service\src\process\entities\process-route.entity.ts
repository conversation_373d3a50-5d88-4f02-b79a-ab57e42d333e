import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 工艺路线状态枚举
 */
export enum ProcessRouteStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived'
}

/**
 * 工序类型枚举
 */
export enum OperationType {
  MACHINING = 'machining',
  ASSEMBLY = 'assembly',
  INSPECTION = 'inspection',
  PACKAGING = 'packaging',
  TRANSPORT = 'transport',
  SETUP = 'setup',
  QUALITY_CHECK = 'quality_check',
  MAINTENANCE = 'maintenance'
}

/**
 * 工序状态枚举
 */
export enum OperationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
  FAILED = 'failed'
}

/**
 * 工艺路线实体
 */
@Entity('process_routes')
export class ProcessRoute {
  @ApiProperty({ description: '工艺路线ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '工艺路线编码' })
  @Column({ name: 'route_code', length: 50, unique: true })
  routeCode: string;

  @ApiProperty({ description: '工艺路线名称' })
  @Column({ name: 'route_name', length: 200 })
  routeName: string;

  @ApiProperty({ description: '产品编码' })
  @Column({ name: 'product_code', length: 100 })
  productCode: string;

  @ApiProperty({ description: '产品名称' })
  @Column({ name: 'product_name', length: 200 })
  productName: string;

  @ApiProperty({ description: '版本号' })
  @Column({ type: 'int', default: 1 })
  version: number;

  @ApiProperty({ description: '状态', enum: ProcessRouteStatus })
  @Column({ type: 'enum', enum: ProcessRouteStatus, default: ProcessRouteStatus.DRAFT })
  status: ProcessRouteStatus;

  @ApiProperty({ description: '工艺路线描述' })
  @Column({ type: 'text', nullable: true })
  description: string;

  @ApiProperty({ description: '标准工时(分钟)' })
  @Column({ name: 'standard_time', type: 'int' })
  standardTime: number;

  @ApiProperty({ description: '准备时间(分钟)' })
  @Column({ name: 'setup_time', type: 'int', default: 0 })
  setupTime: number;

  @ApiProperty({ description: '生效日期' })
  @Column({ name: 'effective_date', type: 'date' })
  effectiveDate: Date;

  @ApiProperty({ description: '失效日期' })
  @Column({ name: 'expiry_date', type: 'date', nullable: true })
  expiryDate: Date;

  @ApiProperty({ description: '创建人' })
  @Column({ name: 'created_by', length: 100 })
  createdBy: string;

  @ApiProperty({ description: '更新人' })
  @Column({ name: 'updated_by', length: 100, nullable: true })
  updatedBy: string;

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @OneToMany(() => ProcessOperation, operation => operation.processRoute)
  operations: ProcessOperation[];

  // 计算属性
  get totalOperations(): number {
    return this.operations ? this.operations.length : 0;
  }

  get isActive(): boolean {
    const now = new Date();
    return this.status === ProcessRouteStatus.ACTIVE &&
           this.effectiveDate <= now &&
           (!this.expiryDate || this.expiryDate >= now);
  }
}

/**
 * 工序实体
 */
@Entity('process_operations')
export class ProcessOperation {
  @ApiProperty({ description: '工序ID' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: '工艺路线ID' })
  @Column({ name: 'process_route_id' })
  processRouteId: string;

  @ApiProperty({ description: '工序编码' })
  @Column({ name: 'operation_code', length: 50 })
  operationCode: string;

  @ApiProperty({ description: '工序名称' })
  @Column({ name: 'operation_name', length: 200 })
  operationName: string;

  @ApiProperty({ description: '工序序号' })
  @Column({ name: 'sequence_number', type: 'int' })
  sequenceNumber: number;

  @ApiProperty({ description: '工序类型', enum: OperationType })
  @Column({ name: 'operation_type', type: 'enum', enum: OperationType })
  operationType: OperationType;

  @ApiProperty({ description: '工作中心ID' })
  @Column({ name: 'work_center_id', nullable: true })
  workCenterId: string;

  @ApiProperty({ description: '工作中心名称' })
  @Column({ name: 'work_center_name', length: 200, nullable: true })
  workCenterName: string;

  @ApiProperty({ description: '设备ID' })
  @Column({ name: 'equipment_id', nullable: true })
  equipmentId: string;

  @ApiProperty({ description: '设备名称' })
  @Column({ name: 'equipment_name', length: 200, nullable: true })
  equipmentName: string;

  @ApiProperty({ description: '标准工时(分钟)' })
  @Column({ name: 'standard_time', type: 'int' })
  standardTime: number;

  @ApiProperty({ description: '准备时间(分钟)' })
  @Column({ name: 'setup_time', type: 'int', default: 0 })
  setupTime: number;

  @ApiProperty({ description: '等待时间(分钟)' })
  @Column({ name: 'wait_time', type: 'int', default: 0 })
  waitTime: number;

  @ApiProperty({ description: '移动时间(分钟)' })
  @Column({ name: 'move_time', type: 'int', default: 0 })
  moveTime: number;

  @ApiProperty({ description: '工序描述' })
  @Column({ type: 'text', nullable: true })
  description: string;

  @ApiProperty({ description: '工艺参数' })
  @Column({ name: 'process_parameters', type: 'json', nullable: true })
  processParameters: any;

  @ApiProperty({ description: '质量检查点' })
  @Column({ name: 'quality_checkpoints', type: 'json', nullable: true })
  qualityCheckpoints: any;

  @ApiProperty({ description: '所需技能' })
  @Column({ name: 'required_skills', type: 'json', nullable: true })
  requiredSkills: any;

  @ApiProperty({ description: '工具清单' })
  @Column({ name: 'tool_list', type: 'json', nullable: true })
  toolList: any;

  @ApiProperty({ description: '物料清单' })
  @Column({ name: 'material_list', type: 'json', nullable: true })
  materialList: any;

  @ApiProperty({ description: '是否关键工序' })
  @Column({ name: 'is_critical', type: 'boolean', default: false })
  isCritical: boolean;

  @ApiProperty({ description: '是否可并行' })
  @Column({ name: 'is_parallel', type: 'boolean', default: false })
  isParallel: boolean;

  @ApiProperty({ description: '前置工序' })
  @Column({ name: 'predecessor_operations', type: 'json', nullable: true })
  predecessorOperations: string[];

  @ApiProperty({ description: '后续工序' })
  @Column({ name: 'successor_operations', type: 'json', nullable: true })
  successorOperations: string[];

  @ApiProperty({ description: '创建时间' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  processRoute: ProcessRoute;

  // 计算属性
  get totalTime(): number {
    return this.standardTime + this.setupTime + this.waitTime + this.moveTime;
  }

  get canStart(): boolean {
    // 检查前置工序是否完成（这里需要实际的执行状态数据）
    return true; // 简化实现
  }
}
