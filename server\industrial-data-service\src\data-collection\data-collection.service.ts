import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThan } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DataPoint } from './entities/data-point.entity';
import { CollectionTask } from './entities/collection-task.entity';
import { ProtocolService } from '../protocol/protocol.service';
import { StorageService } from '../storage/storage.service';
import { DataCollectionGateway } from './data-collection.gateway';
import * as moment from 'moment';

/**
 * 数据采集服务
 * 负责从工业设备采集数据并存储
 */
@Injectable()
export class DataCollectionService {
  private readonly logger = new Logger(DataCollectionService.name);
  private activeTasks: Map<string, NodeJS.Timeout> = new Map();
  private collectionStats = {
    totalPoints: 0,
    successfulCollections: 0,
    failedCollections: 0,
    lastCollectionTime: new Date(),
  };

  constructor(
    @InjectRepository(DataPoint)
    private dataPointRepository: Repository<DataPoint>,
    @InjectRepository(CollectionTask)
    private collectionTaskRepository: Repository<CollectionTask>,
    private protocolService: ProtocolService,
    private storageService: StorageService,
    private dataCollectionGateway: DataCollectionGateway,
  ) {
    this.initializeCollectionTasks();
  }

  /**
   * 初始化数据采集任务
   */
  private async initializeCollectionTasks(): Promise<void> {
    try {
      const tasks = await this.collectionTaskRepository.find({
        where: { enabled: true },
      });

      for (const task of tasks) {
        await this.startCollectionTask(task);
      }

      this.logger.log(`已启动 ${tasks.length} 个数据采集任务`);
    } catch (error) {
      this.logger.error('初始化数据采集任务失败', error);
    }
  }

  /**
   * 启动数据采集任务
   * @param task 采集任务
   */
  private async startCollectionTask(task: CollectionTask): Promise<void> {
    // 停止现有任务
    if (this.activeTasks.has(task.id)) {
      clearInterval(this.activeTasks.get(task.id));
    }

    // 启动新任务
    const timer = setInterval(async () => {
      await this.collectDataForTask(task);
    }, task.interval * 1000);

    this.activeTasks.set(task.id, timer);
    this.logger.log(`启动数据采集任务: ${task.name} (间隔: ${task.interval}秒)`);
  }

  /**
   * 为特定任务采集数据
   * @param task 采集任务
   */
  private async collectDataForTask(task: CollectionTask): Promise<void> {
    try {
      const deviceConfig = JSON.parse(task.deviceConfig);
      const tagIds = JSON.parse(task.tagIds);

      // 从设备采集数据
      const dataPoints = await this.protocolService.readMultipleTags(
        task.deviceId,
        tagIds,
        deviceConfig
      );

      // 存储数据点
      for (const dataPoint of dataPoints) {
        await this.storeDataPoint(dataPoint, task);
      }

      // 更新统计信息
      this.collectionStats.successfulCollections++;
      this.collectionStats.totalPoints += dataPoints.length;
      this.collectionStats.lastCollectionTime = new Date();

      // 实时推送数据
      this.dataCollectionGateway.broadcastDataPoints(dataPoints);

      this.logger.debug(`任务 ${task.name} 采集到 ${dataPoints.length} 个数据点`);

    } catch (error) {
      this.collectionStats.failedCollections++;
      this.logger.error(`任务 ${task.name} 数据采集失败`, error);
    }
  }

  /**
   * 存储数据点
   * @param dataPoint 数据点
   * @param task 采集任务
   */
  private async storeDataPoint(dataPoint: any, task: CollectionTask): Promise<void> {
    try {
      const entity = this.dataPointRepository.create({
        deviceId: task.deviceId,
        tagId: dataPoint.tagId,
        tagName: dataPoint.tagName || dataPoint.tagId,
        timestamp: dataPoint.timestamp || new Date(),
        value: dataPoint.value,
        dataType: this.getDataType(dataPoint.value),
        quality: dataPoint.quality || 'good',
        metadata: dataPoint.metadata,
        unit: dataPoint.unit,
        sourceTimestamp: dataPoint.sourceTimestamp,
        collectionMethod: 'polling',
      });

      await this.dataPointRepository.save(entity);

      // 同时存储到时序数据库
      await this.storageService.storeTimeSeriesData(entity);

    } catch (error) {
      this.logger.error('存储数据点失败', error);
    }
  }

  /**
   * 获取数据类型
   * @param value 值
   * @returns 数据类型
   */
  private getDataType(value: any): string {
    if (typeof value === 'boolean') return 'boolean';
    if (typeof value === 'number') {
      return Number.isInteger(value) ? 'integer' : 'float';
    }
    if (typeof value === 'string') return 'string';
    if (value instanceof Date) return 'datetime';
    return 'object';
  }

  /**
   * 创建数据采集任务
   * @param taskData 任务数据
   * @returns 创建的任务
   */
  async createCollectionTask(taskData: any): Promise<CollectionTask> {
    const task = this.collectionTaskRepository.create({
      name: taskData.name,
      description: taskData.description,
      deviceId: taskData.deviceId,
      deviceConfig: JSON.stringify(taskData.deviceConfig),
      tagIds: JSON.stringify(taskData.tagIds),
      interval: taskData.interval || 10,
      enabled: taskData.enabled !== false,
    });

    const savedTask = await this.collectionTaskRepository.save(task);

    if (savedTask.enabled) {
      await this.startCollectionTask(savedTask);
    }

    this.logger.log(`创建数据采集任务: ${savedTask.name}`);
    return savedTask;
  }

  /**
   * 更新数据采集任务
   * @param id 任务ID
   * @param updateData 更新数据
   * @returns 更新后的任务
   */
  async updateCollectionTask(id: string, updateData: any): Promise<CollectionTask> {
    const task = await this.collectionTaskRepository.findOne({ where: { id } });
    if (!task) {
      throw new Error(`数据采集任务不存在: ${id}`);
    }

    // 停止现有任务
    if (this.activeTasks.has(id)) {
      clearInterval(this.activeTasks.get(id));
      this.activeTasks.delete(id);
    }

    // 更新任务
    Object.assign(task, updateData);
    if (updateData.deviceConfig) {
      task.deviceConfig = JSON.stringify(updateData.deviceConfig);
    }
    if (updateData.tagIds) {
      task.tagIds = JSON.stringify(updateData.tagIds);
    }

    const updatedTask = await this.collectionTaskRepository.save(task);

    // 重新启动任务
    if (updatedTask.enabled) {
      await this.startCollectionTask(updatedTask);
    }

    this.logger.log(`更新数据采集任务: ${updatedTask.name}`);
    return updatedTask;
  }

  /**
   * 删除数据采集任务
   * @param id 任务ID
   */
  async deleteCollectionTask(id: string): Promise<void> {
    // 停止任务
    if (this.activeTasks.has(id)) {
      clearInterval(this.activeTasks.get(id));
      this.activeTasks.delete(id);
    }

    // 删除任务
    await this.collectionTaskRepository.delete(id);
    this.logger.log(`删除数据采集任务: ${id}`);
  }

  /**
   * 获取所有采集任务
   * @returns 采集任务列表
   */
  async getAllCollectionTasks(): Promise<CollectionTask[]> {
    return this.collectionTaskRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 获取设备的历史数据
   * @param deviceId 设备ID
   * @param tagId 标签ID
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param limit 限制数量
   * @returns 历史数据
   */
  async getHistoricalData(
    deviceId: string,
    tagId?: string,
    startTime?: Date,
    endTime?: Date,
    limit: number = 1000
  ): Promise<DataPoint[]> {
    const query = this.dataPointRepository.createQueryBuilder('dp')
      .where('dp.deviceId = :deviceId', { deviceId });

    if (tagId) {
      query.andWhere('dp.tagId = :tagId', { tagId });
    }

    if (startTime && endTime) {
      query.andWhere('dp.timestamp BETWEEN :startTime AND :endTime', {
        startTime,
        endTime,
      });
    } else if (startTime) {
      query.andWhere('dp.timestamp >= :startTime', { startTime });
    }

    return query
      .orderBy('dp.timestamp', 'DESC')
      .limit(limit)
      .getMany();
  }

  /**
   * 获取实时数据
   * @param deviceId 设备ID
   * @param tagIds 标签ID列表
   * @returns 实时数据
   */
  async getRealTimeData(deviceId: string, tagIds?: string[]): Promise<DataPoint[]> {
    const query = this.dataPointRepository.createQueryBuilder('dp')
      .where('dp.deviceId = :deviceId', { deviceId });

    if (tagIds && tagIds.length > 0) {
      query.andWhere('dp.tagId IN (:...tagIds)', { tagIds });
    }

    // 获取每个标签的最新数据
    const subQuery = this.dataPointRepository.createQueryBuilder('dp2')
      .select('MAX(dp2.timestamp)', 'maxTimestamp')
      .addSelect('dp2.tagId', 'tagId')
      .where('dp2.deviceId = :deviceId', { deviceId })
      .groupBy('dp2.tagId');

    if (tagIds && tagIds.length > 0) {
      subQuery.andWhere('dp2.tagId IN (:...tagIds)', { tagIds });
    }

    const latestTimestamps = await subQuery.getRawMany();

    if (latestTimestamps.length === 0) {
      return [];
    }

    const conditions = latestTimestamps.map(lt => 
      `(dp.tagId = '${lt.tagId}' AND dp.timestamp = '${moment(lt.maxTimestamp).format('YYYY-MM-DD HH:mm:ss.SSS')}')`
    ).join(' OR ');

    return query
      .andWhere(`(${conditions})`)
      .orderBy('dp.tagId', 'ASC')
      .getMany();
  }

  /**
   * 获取采集统计信息
   * @returns 统计信息
   */
  getCollectionStats(): any {
    return {
      ...this.collectionStats,
      activeTasks: this.activeTasks.size,
    };
  }

  /**
   * 手动触发数据采集
   * @param taskId 任务ID
   */
  async triggerManualCollection(taskId: string): Promise<void> {
    const task = await this.collectionTaskRepository.findOne({ where: { id: taskId } });
    if (!task) {
      throw new Error(`数据采集任务不存在: ${taskId}`);
    }

    await this.collectDataForTask(task);
    this.logger.log(`手动触发数据采集: ${task.name}`);
  }

  /**
   * 定时清理过期数据
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupExpiredData(): Promise<void> {
    try {
      const retentionDays = 30; // 保留30天数据
      const cutoffDate = moment().subtract(retentionDays, 'days').toDate();

      const result = await this.dataPointRepository.delete({
        timestamp: MoreThan(cutoffDate),
      });

      this.logger.log(`清理过期数据完成，删除 ${result.affected} 条记录`);
    } catch (error) {
      this.logger.error('清理过期数据失败', error);
    }
  }

  /**
   * 停止所有采集任务
   */
  async stopAllTasks(): Promise<void> {
    this.activeTasks.forEach((timer, taskId) => {
      clearInterval(timer);
      this.logger.log(`停止数据采集任务: ${taskId}`);
    });
    this.activeTasks.clear();
  }
}
