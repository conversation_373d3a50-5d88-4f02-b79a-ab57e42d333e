# 自然语言生成场景功能设计与实现方案

## 概述

基于现有项目架构和自然语言生成场景功能分析，本方案将在原有DL引擎基础上设计与实现完整的自然语言生成场景功能，包括编辑器界面、服务器端API、视觉脚本集成等全方位的实现。

## 1. 整体架构设计

### 1.1 系统架构图

```mermaid
graph TB
    subgraph "编辑器层"
        A[自然语言场景生成面板] --> B[场景预览组件]
        A --> C[生成参数配置]
        A --> D[历史记录管理]
    end
    
    subgraph "引擎层"
        E[NLPSceneGenerator] --> F[AIContentGenerator]
        E --> G[NaturalLanguageProcessor]
        F --> H[Text3DGenerator]
        G --> I[语义分析器]
    end
    
    subgraph "服务器层"
        J[场景生成API] --> K[AI模型服务]
        J --> L[场景存储服务]
        J --> M[用户管理服务]
    end
    
    subgraph "视觉脚本层"
        N[NLP节点库] --> O[场景生成节点]
        N --> P[文本处理节点]
        N --> Q[AI模型节点]
    end
    
    A --> E
    E --> J
    N --> E
    J --> K
    F --> L
```

### 1.2 核心组件关系

- **编辑器界面**: 提供用户友好的自然语言输入和场景生成界面
- **引擎核心**: 处理自然语言理解和3D场景生成逻辑
- **服务器端**: 提供AI模型服务和数据存储支持
- **视觉脚本**: 支持可视化编程方式使用自然语言生成功能

## 2. 编辑器界面实现

### 2.1 自然语言场景生成面板

创建专门的面板组件，集成到现有编辑器架构中：

```typescript
// editor/src/components/panels/NLPSceneGenerationPanel.tsx
import React, { useState, useCallback, useRef } from 'react';
import { 
  Card, Input, Button, Select, Slider, Space, Typography, 
  Divider, Progress, Alert, List, Tag, Tooltip, Row, Col 
} from 'antd';
import { 
  SendOutlined, HistoryOutlined, SettingOutlined, 
  EyeOutlined, SaveOutlined, ReloadOutlined 
} from '@ant-design/icons';

interface NLPSceneGenerationPanelProps {
  onSceneGenerated?: (scene: any) => void;
  onPreviewScene?: (scene: any) => void;
}

export const NLPSceneGenerationPanel: React.FC<NLPSceneGenerationPanelProps> = ({
  onSceneGenerated,
  onPreviewScene
}) => {
  const [inputText, setInputText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationHistory, setGenerationHistory] = useState([]);
  const [selectedStyle, setSelectedStyle] = useState('realistic');
  const [qualityLevel, setQualityLevel] = useState(80);
  const [maxObjects, setMaxObjects] = useState(50);
  
  // 生成场景
  const handleGenerateScene = useCallback(async () => {
    if (!inputText.trim()) return;
    
    setIsGenerating(true);
    setGenerationProgress(0);
    
    try {
      // 调用引擎的自然语言场景生成功能
      const nlpGenerator = window.dlEngine?.getSystem('NLPSceneGenerator');
      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }
      
      // 配置生成参数
      const options = {
        style: selectedStyle,
        constraints: {
          maxObjects,
          maxPolygons: qualityLevel * 1000,
          targetFrameRate: 60
        },
        onProgress: (progress: number) => {
          setGenerationProgress(progress);
        }
      };
      
      // 生成场景
      const scene = await nlpGenerator.generateSceneFromNaturalLanguage(
        inputText, 
        options
      );
      
      // 添加到历史记录
      const historyItem = {
        id: Date.now(),
        text: inputText,
        style: selectedStyle,
        timestamp: new Date(),
        scene
      };
      setGenerationHistory(prev => [historyItem, ...prev.slice(0, 9)]);
      
      // 回调通知
      onSceneGenerated?.(scene);
      
    } catch (error) {
      console.error('场景生成失败:', error);
      // 显示错误提示
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  }, [inputText, selectedStyle, qualityLevel, maxObjects, onSceneGenerated]);
  
  return (
    <div className="nlp-scene-generation-panel">
      <Card title="自然语言场景生成" size="small">
        {/* 文本输入区域 */}
        <Space direction="vertical" style={{ width: '100%' }}>
          <Input.TextArea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="请描述您想要创建的场景，例如：创建一个现代化的办公室，包含玻璃桌子、舒适的椅子和绿色植物..."
            rows={4}
            maxLength={500}
            showCount
          />
          
          {/* 生成参数配置 */}
          <Card size="small" title="生成参数">
            <Row gutter={16}>
              <Col span={12}>
                <Typography.Text>风格:</Typography.Text>
                <Select
                  value={selectedStyle}
                  onChange={setSelectedStyle}
                  style={{ width: '100%' }}
                  options={[
                    { label: '写实风格', value: 'realistic' },
                    { label: '卡通风格', value: 'cartoon' },
                    { label: '简约风格', value: 'minimalist' },
                    { label: '科幻风格', value: 'scifi' },
                    { label: '奇幻风格', value: 'fantasy' }
                  ]}
                />
              </Col>
              <Col span={12}>
                <Typography.Text>质量等级: {qualityLevel}</Typography.Text>
                <Slider
                  value={qualityLevel}
                  onChange={setQualityLevel}
                  min={20}
                  max={100}
                  step={10}
                />
              </Col>
            </Row>
            
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Typography.Text>最大对象数: {maxObjects}</Typography.Text>
                <Slider
                  value={maxObjects}
                  onChange={setMaxObjects}
                  min={10}
                  max={100}
                  step={5}
                />
              </Col>
            </Row>
          </Card>
          
          {/* 操作按钮 */}
          <Space>
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleGenerateScene}
              loading={isGenerating}
              disabled={!inputText.trim()}
            >
              生成场景
            </Button>
            <Button icon={<EyeOutlined />}>预览</Button>
            <Button icon={<SaveOutlined />}>保存配置</Button>
          </Space>
          
          {/* 生成进度 */}
          {isGenerating && (
            <Card size="small">
              <Progress 
                percent={generationProgress} 
                status="active"
                format={(percent) => `生成中... ${percent}%`}
              />
            </Card>
          )}
        </Space>
      </Card>
      
      {/* 历史记录 */}
      <Card title="生成历史" size="small" style={{ marginTop: 16 }}>
        <List
          size="small"
          dataSource={generationHistory}
          renderItem={(item: any) => (
            <List.Item
              actions={[
                <Button size="small" icon={<EyeOutlined />}>预览</Button>,
                <Button size="small" icon={<ReloadOutlined />}>重新生成</Button>
              ]}
            >
              <List.Item.Meta
                title={
                  <Space>
                    <Typography.Text ellipsis style={{ maxWidth: 200 }}>
                      {item.text}
                    </Typography.Text>
                    <Tag color="blue">{item.style}</Tag>
                  </Space>
                }
                description={item.timestamp.toLocaleString()}
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};
```

### 2.2 集成到编辑器主界面

将自然语言场景生成面板集成到现有的编辑器面板系统中：

```typescript
// editor/src/components/panels/index.ts
export { NLPSceneGenerationPanel } from './NLPSceneGenerationPanel';

// editor/src/core/PanelRegistry.ts
import { NLPSceneGenerationPanel } from '../components/panels/NLPSceneGenerationPanel';

// 注册自然语言场景生成面板
panelRegistry.registerPanel({
  id: 'nlp-scene-generation',
  name: '自然语言场景生成',
  component: NLPSceneGenerationPanel,
  icon: 'robot',
  category: 'ai',
  defaultPosition: 'left',
  defaultSize: { width: 350, height: 600 }
});
```

### 2.3 场景预览组件

创建专门的场景预览组件，支持实时预览生成的场景：

```typescript
// editor/src/components/scene/NLPScenePreview.tsx
import React, { useEffect, useRef, useState } from 'react';
import { Card, Button, Space, Slider, Typography } from 'antd';
import { FullscreenOutlined, RotateLeftOutlined, ZoomInOutlined } from '@ant-design/icons';

interface NLPScenePreviewProps {
  scene?: any;
  onFullscreen?: () => void;
}

export const NLPScenePreview: React.FC<NLPScenePreviewProps> = ({
  scene,
  onFullscreen
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [cameraDistance, setCameraDistance] = useState(10);
  const [autoRotate, setAutoRotate] = useState(true);
  
  useEffect(() => {
    if (!scene || !canvasRef.current) return;
    
    // 初始化预览渲染器
    const previewRenderer = new ScenePreviewRenderer(canvasRef.current);
    previewRenderer.setScene(scene);
    previewRenderer.setCameraDistance(cameraDistance);
    previewRenderer.setAutoRotate(autoRotate);
    
    return () => {
      previewRenderer.dispose();
    };
  }, [scene, cameraDistance, autoRotate]);
  
  return (
    <Card 
      title="场景预览" 
      size="small"
      extra={
        <Space>
          <Button size="small" icon={<RotateLeftOutlined />} 
                  onClick={() => setAutoRotate(!autoRotate)}>
            {autoRotate ? '停止旋转' : '自动旋转'}
          </Button>
          <Button size="small" icon={<FullscreenOutlined />} 
                  onClick={onFullscreen}>
            全屏
          </Button>
        </Space>
      }
    >
      <div style={{ position: 'relative' }}>
        <canvas 
          ref={canvasRef}
          style={{ 
            width: '100%', 
            height: '300px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px'
          }}
        />
        
        <div style={{ marginTop: 8 }}>
          <Typography.Text>相机距离:</Typography.Text>
          <Slider
            value={cameraDistance}
            onChange={setCameraDistance}
            min={5}
            max={50}
            step={1}
          />
        </div>
      </div>
    </Card>
  );
};
```

## 3. 引擎核心实现

### 3.1 自然语言场景生成器

创建核心的自然语言场景生成器系统：

```typescript
// engine/src/ai/NLPSceneGenerator.ts
import { System } from '../core/System';
import { NaturalLanguageProcessor } from './nlp/NaturalLanguageProcessor';
import { AIContentGenerator } from './AIContentGenerator';
import { Scene } from '../scene/Scene';
import { EventEmitter } from '../utils/EventEmitter';

export interface NLPGenerationOptions {
  style?: string;
  constraints?: {
    maxObjects?: number;
    maxPolygons?: number;
    targetFrameRate?: number;
  };
  onProgress?: (progress: number) => void;
}

export interface SceneElements {
  objects: string[];
  locations: string[];
  attributes: string[];
  actions: string[];
  mood: string;
  style: string;
}

export class NLPSceneGenerator extends System {
  static readonly NAME = 'NLPSceneGenerator';
  
  private nlpProcessor: NaturalLanguageProcessor;
  private contentGenerator: AIContentGenerator;
  private eventEmitter: EventEmitter;
  
  constructor() {
    super(350); // 设置系统优先级
    
    this.nlpProcessor = new NaturalLanguageProcessor({
      enableEntityRecognition: true,
      enableIntentClassification: true,
      enableSentimentAnalysis: true
    });
    
    this.contentGenerator = new AIContentGenerator({
      debug: true,
      text3D: {
        timeout: 30000,
        defaultStyle: 'realistic'
      }
    });
    
    this.eventEmitter = new EventEmitter();
  }
  
  /**
   * 从自然语言生成场景
   */
  public async generateSceneFromNaturalLanguage(
    userInput: string,
    options: NLPGenerationOptions = {}
  ): Promise<Scene> {
    try {
      // 第一步：自然语言理解 (10%)
      options.onProgress?.(10);
      const understanding = await this.nlpProcessor.understand(userInput);
      
      // 第二步：提取场景要素 (20%)
      options.onProgress?.(20);
      const sceneElements = this.extractSceneElements(understanding);
      
      // 第三步：生成场景描述 (30%)
      options.onProgress?.(30);
      const sceneDescription = this.buildSceneDescription(sceneElements);
      
      // 第四步：生成3D场景 (80%)
      const scene = await this.contentGenerator.generateSceneFromText(
        sceneDescription,
        options.style || 'realistic',
        options.constraints || {}
      );
      
      // 第五步：后处理和优化 (100%)
      options.onProgress?.(90);
      await this.postProcessScene(scene, understanding.sentiment);
      
      options.onProgress?.(100);
      
      // 触发事件
      this.eventEmitter.emit('sceneGenerated', {
        userInput,
        scene,
        understanding,
        sceneElements
      });
      
      return scene;
      
    } catch (error) {
      console.error('自然语言场景生成失败:', error);
      this.eventEmitter.emit('generationError', { userInput, error });
      throw error;
    }
  }
  
  /**
   * 提取场景要素
   */
  private extractSceneElements(understanding: any): SceneElements {
    const entities = understanding.entities || [];
    
    return {
      objects: entities.filter((e: any) => e.type === 'OBJECT').map((e: any) => e.text),
      locations: entities.filter((e: any) => e.type === 'LOCATION').map((e: any) => e.text),
      attributes: entities.filter((e: any) => e.type === 'ATTRIBUTE').map((e: any) => e.text),
      actions: entities.filter((e: any) => e.type === 'ACTION').map((e: any) => e.text),
      mood: understanding.sentiment || 'neutral',
      style: this.inferStyleFromText(understanding.text)
    };
  }
  
  /**
   * 构建场景描述
   */
  private buildSceneDescription(elements: SceneElements): string {
    let description = '';
    
    // 基础场景描述
    if (elements.locations.length > 0) {
      description += `创建一个${elements.locations[0]}场景，`;
    }
    
    // 添加对象描述
    if (elements.objects.length > 0) {
      description += `包含${elements.objects.join('、')}，`;
    }
    
    // 添加属性描述
    if (elements.attributes.length > 0) {
      description += `具有${elements.attributes.join('、')}的特点，`;
    }
    
    // 添加情感氛围
    switch (elements.mood) {
      case 'positive':
        description += '营造温馨愉悦的氛围';
        break;
      case 'negative':
        description += '营造严肃冷静的氛围';
        break;
      default:
        description += '营造自然舒适的氛围';
    }
    
    return description;
  }
  
  /**
   * 从文本推断风格
   */
  private inferStyleFromText(text: string): string {
    const lowerText = text.toLowerCase();
    
    if (/科幻|未来|太空|机器人/.test(text)) return 'scifi';
    if (/卡通|可爱|童话|动画/.test(text)) return 'cartoon';
    if (/简约|极简|现代|简洁/.test(text)) return 'minimalist';
    if (/魔法|奇幻|龙|城堡/.test(text)) return 'fantasy';
    
    return 'realistic';
  }
  
  /**
   * 后处理场景
   */
  private async postProcessScene(scene: Scene, sentiment: string): Promise<void> {
    // 根据情感调整场景氛围
    if (sentiment === 'positive') {
      this.enhanceBrightness(scene);
      this.addWarmLighting(scene);
    } else if (sentiment === 'negative') {
      this.reduceBrightness(scene);
      this.addCoolLighting(scene);
    }
    
    // 优化场景性能
    await this.optimizeScenePerformance(scene);
  }
  
  /**
   * 增强亮度
   */
  private enhanceBrightness(scene: Scene): void {
    // 实现亮度增强逻辑
  }
  
  /**
   * 添加暖色光照
   */
  private addWarmLighting(scene: Scene): void {
    // 实现暖色光照逻辑
  }
  
  /**
   * 降低亮度
   */
  private reduceBrightness(scene: Scene): void {
    // 实现亮度降低逻辑
  }
  
  /**
   * 添加冷色光照
   */
  private addCoolLighting(scene: Scene): void {
    // 实现冷色光照逻辑
  }
  
  /**
   * 优化场景性能
   */
  private async optimizeScenePerformance(scene: Scene): Promise<void> {
    // 实现性能优化逻辑
  }
  
  /**
   * 添加事件监听器
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }
  
  /**
   * 移除事件监听器
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
```

## 4. 服务器端API实现

### 4.1 自然语言场景生成API

创建专门的API服务来处理自然语言场景生成请求：

```typescript
// server/nlp-scene-service/src/controllers/NLPSceneController.ts
import { Controller, Post, Get, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '../guards/auth.guard';
import { RateLimitGuard } from '../guards/rate-limit.guard';
import { NLPSceneService } from '../services/NLPSceneService';

@ApiTags('自然语言场景生成')
@Controller('api/v1/nlp-scene')
@UseGuards(AuthGuard, RateLimitGuard)
@ApiBearerAuth()
export class NLPSceneController {
  constructor(private readonly nlpSceneService: NLPSceneService) {}

  @Post('generate')
  @ApiOperation({ summary: '生成场景' })
  @ApiResponse({ status: 200, description: '场景生成成功' })
  async generateScene(@Body() request: GenerateSceneRequest) {
    return await this.nlpSceneService.generateScene(request);
  }

  @Post('preview')
  @ApiOperation({ summary: '预览场景' })
  async previewScene(@Body() request: PreviewSceneRequest) {
    return await this.nlpSceneService.previewScene(request);
  }

  @Get('history/:userId')
  @ApiOperation({ summary: '获取用户生成历史' })
  async getGenerationHistory(
    @Param('userId') userId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10
  ) {
    return await this.nlpSceneService.getGenerationHistory(userId, page, limit);
  }

  @Post('save')
  @ApiOperation({ summary: '保存生成的场景' })
  async saveGeneratedScene(@Body() request: SaveSceneRequest) {
    return await this.nlpSceneService.saveGeneratedScene(request);
  }

  @Get('templates')
  @ApiOperation({ summary: '获取场景模板' })
  async getSceneTemplates(@Query('category') category?: string) {
    return await this.nlpSceneService.getSceneTemplates(category);
  }
}

// 请求和响应DTO
export interface GenerateSceneRequest {
  text: string;
  style: string;
  quality: number;
  maxObjects: number;
  userId: string;
  projectId?: string;
}

export interface PreviewSceneRequest {
  text: string;
  style: string;
  lowQuality?: boolean;
}

export interface SaveSceneRequest {
  sceneData: any;
  name: string;
  description?: string;
  userId: string;
  projectId?: string;
  tags?: string[];
}
```

### 4.2 NLP场景生成服务

```typescript
// server/nlp-scene-service/src/services/NLPSceneService.ts
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GeneratedScene } from '../entities/GeneratedScene';
import { SceneTemplate } from '../entities/SceneTemplate';
import { AIModelService } from './AIModelService';
import { SceneStorageService } from './SceneStorageService';

@Injectable()
export class NLPSceneService {
  private readonly logger = new Logger(NLPSceneService.name);

  constructor(
    @InjectRepository(GeneratedScene)
    private generatedSceneRepository: Repository<GeneratedScene>,
    @InjectRepository(SceneTemplate)
    private sceneTemplateRepository: Repository<SceneTemplate>,
    private aiModelService: AIModelService,
    private sceneStorageService: SceneStorageService
  ) {}

  /**
   * 生成场景
   */
  async generateScene(request: GenerateSceneRequest) {
    try {
      this.logger.log(`开始生成场景: ${request.text}`);

      // 1. 自然语言理解
      const understanding = await this.aiModelService.understandText(request.text);

      // 2. 场景结构生成
      const sceneStructure = await this.aiModelService.generateSceneStructure(
        understanding,
        request.style
      );

      // 3. 3D内容生成
      const sceneContent = await this.aiModelService.generate3DContent(
        sceneStructure,
        {
          quality: request.quality,
          maxObjects: request.maxObjects
        }
      );

      // 4. 保存生成记录
      const generatedScene = new GeneratedScene();
      generatedScene.inputText = request.text;
      generatedScene.style = request.style;
      generatedScene.userId = request.userId;
      generatedScene.projectId = request.projectId;
      generatedScene.sceneData = sceneContent;
      generatedScene.understanding = understanding;
      generatedScene.createdAt = new Date();

      await this.generatedSceneRepository.save(generatedScene);

      // 5. 存储场景文件
      const sceneUrl = await this.sceneStorageService.saveScene(
        sceneContent,
        `scene_${generatedScene.id}`
      );

      this.logger.log(`场景生成完成: ${generatedScene.id}`);

      return {
        success: true,
        sceneId: generatedScene.id,
        sceneUrl,
        sceneData: sceneContent,
        understanding,
        metadata: {
          generationTime: Date.now() - generatedScene.createdAt.getTime(),
          objectCount: sceneContent.entities?.length || 0,
          polygonCount: this.calculatePolygonCount(sceneContent)
        }
      };

    } catch (error) {
      this.logger.error(`场景生成失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 预览场景
   */
  async previewScene(request: PreviewSceneRequest) {
    try {
      // 使用低质量快速生成预览
      const understanding = await this.aiModelService.understandText(request.text);

      const previewData = await this.aiModelService.generatePreview(
        understanding,
        request.style,
        { lowQuality: true }
      );

      return {
        success: true,
        previewData,
        understanding: {
          entities: understanding.entities,
          sentiment: understanding.sentiment,
          intent: understanding.intent
        }
      };

    } catch (error) {
      this.logger.error(`场景预览失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取生成历史
   */
  async getGenerationHistory(userId: string, page: number, limit: number) {
    const [items, total] = await this.generatedSceneRepository.findAndCount({
      where: { userId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
      select: ['id', 'inputText', 'style', 'createdAt', 'projectId']
    });

    return {
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 保存生成的场景
   */
  async saveGeneratedScene(request: SaveSceneRequest) {
    try {
      const sceneUrl = await this.sceneStorageService.saveScene(
        request.sceneData,
        `saved_scene_${Date.now()}`
      );

      // 可以保存到项目或用户的场景库中
      // 这里简化处理，实际可能需要更复杂的逻辑

      return {
        success: true,
        sceneUrl,
        message: '场景保存成功'
      };

    } catch (error) {
      this.logger.error(`场景保存失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取场景模板
   */
  async getSceneTemplates(category?: string) {
    const where = category ? { category } : {};

    const templates = await this.sceneTemplateRepository.find({
      where,
      order: { popularity: 'DESC' }
    });

    return {
      success: true,
      templates
    };
  }

  /**
   * 计算多边形数量
   */
  private calculatePolygonCount(sceneData: any): number {
    // 实现多边形计算逻辑
    return sceneData.entities?.reduce((total: number, entity: any) => {
      return total + (entity.polygonCount || 0);
    }, 0) || 0;
  }
}
```

### 4.3 数据库实体定义

```typescript
// server/nlp-scene-service/src/entities/GeneratedScene.ts
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('generated_scenes')
export class GeneratedScene {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('text')
  inputText: string;

  @Column('varchar', { length: 50 })
  style: string;

  @Column('varchar', { length: 100 })
  userId: string;

  @Column('varchar', { length: 100, nullable: true })
  projectId: string;

  @Column('json')
  sceneData: any;

  @Column('json', { nullable: true })
  understanding: any;

  @Column('varchar', { length: 500, nullable: true })
  sceneUrl: string;

  @Column('int', { default: 0 })
  polygonCount: number;

  @Column('int', { default: 0 })
  objectCount: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

// server/nlp-scene-service/src/entities/SceneTemplate.ts
@Entity('scene_templates')
export class SceneTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('varchar', { length: 200 })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('varchar', { length: 50 })
  category: string;

  @Column('text')
  templateText: string;

  @Column('varchar', { length: 50 })
  style: string;

  @Column('json', { nullable: true })
  defaultParameters: any;

  @Column('varchar', { length: 500, nullable: true })
  previewImage: string;

  @Column('int', { default: 0 })
  popularity: number;

  @Column('boolean', { default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

## 5. 视觉脚本系统集成

### 5.1 自然语言场景生成节点

```typescript
// engine/src/visualscript/presets/NLPSceneNodes.ts
import { AsyncNode } from '../nodes/AsyncNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 自然语言场景生成节点
 */
export class NLPSceneGenerationNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/scene/generate',
      category: NodeCategory.AI
    });

    // 输入插槽
    this.addSocket({
      name: 'text',
      direction: SocketDirection.INPUT,
      type: SocketType.STRING,
      label: '场景描述'
    });

    this.addSocket({
      name: 'style',
      direction: SocketDirection.INPUT,
      type: SocketType.STRING,
      label: '风格',
      defaultValue: 'realistic'
    });

    this.addSocket({
      name: 'quality',
      direction: SocketDirection.INPUT,
      type: SocketType.NUMBER,
      label: '质量等级',
      defaultValue: 80
    });

    this.addSocket({
      name: 'maxObjects',
      direction: SocketDirection.INPUT,
      type: SocketType.NUMBER,
      label: '最大对象数',
      defaultValue: 50
    });

    // 输出插槽
    this.addSocket({
      name: 'scene',
      direction: SocketDirection.OUTPUT,
      type: SocketType.OBJECT,
      label: '生成的场景'
    });

    this.addSocket({
      name: 'understanding',
      direction: SocketDirection.OUTPUT,
      type: SocketType.OBJECT,
      label: '语言理解结果'
    });

    this.addSocket({
      name: 'progress',
      direction: SocketDirection.OUTPUT,
      type: SocketType.NUMBER,
      label: '生成进度'
    });

    // 流程插槽
    this.addFlowSocket('success', SocketDirection.OUTPUT, '成功');
    this.addFlowSocket('error', SocketDirection.OUTPUT, '失败');
  }

  async execute(): Promise<any> {
    const text = this.getInputValue('text') as string;
    const style = this.getInputValue('style') as string;
    const quality = this.getInputValue('quality') as number;
    const maxObjects = this.getInputValue('maxObjects') as number;

    if (!text) {
      this.triggerFlow('error');
      return { success: false, error: '场景描述不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      const options = {
        style,
        constraints: {
          maxObjects,
          maxPolygons: quality * 1000,
          targetFrameRate: 60
        },
        onProgress: (progress: number) => {
          this.setOutputValue('progress', progress);
        }
      };

      const scene = await nlpGenerator.generateSceneFromNaturalLanguage(text, options);

      this.setOutputValue('scene', scene);
      this.setOutputValue('progress', 100);

      this.triggerFlow('success');
      return { success: true, scene };

    } catch (error) {
      console.error('自然语言场景生成失败:', error);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }
}

/**
 * 场景理解节点
 */
export class SceneUnderstandingNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/scene/understand',
      category: NodeCategory.AI
    });

    this.addSocket({
      name: 'text',
      direction: SocketDirection.INPUT,
      type: SocketType.STRING,
      label: '输入文本'
    });

    this.addSocket({
      name: 'entities',
      direction: SocketDirection.OUTPUT,
      type: SocketType.ARRAY,
      label: '识别的实体'
    });

    this.addSocket({
      name: 'sentiment',
      direction: SocketDirection.OUTPUT,
      type: SocketType.STRING,
      label: '情感倾向'
    });

    this.addSocket({
      name: 'intent',
      direction: SocketDirection.OUTPUT,
      type: SocketType.STRING,
      label: '意图'
    });

    this.addFlowSocket('success', SocketDirection.OUTPUT, '成功');
    this.addFlowSocket('error', SocketDirection.OUTPUT, '失败');
  }

  async execute(): Promise<any> {
    const text = this.getInputValue('text') as string;

    if (!text) {
      this.triggerFlow('error');
      return { success: false, error: '输入文本不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const nlpProcessor = world.getSystem('NaturalLanguageProcessor');

      if (!nlpProcessor) {
        throw new Error('自然语言处理器未初始化');
      }

      const understanding = await nlpProcessor.understand(text);

      this.setOutputValue('entities', understanding.entities);
      this.setOutputValue('sentiment', understanding.sentiment);
      this.setOutputValue('intent', understanding.intent);

      this.triggerFlow('success');
      return { success: true, understanding };

    } catch (error) {
      console.error('文本理解失败:', error);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册自然语言场景节点
 */
export function registerNLPSceneNodes(registry: NodeRegistry): void {
  // 注册自然语言场景生成节点
  registry.registerNodeType({
    type: 'nlp/scene/generate',
    category: NodeCategory.AI,
    constructor: NLPSceneGenerationNode,
    label: '自然语言场景生成',
    description: '基于自然语言描述生成3D场景',
    icon: 'robot',
    color: '#722ED1',
    tags: ['nlp', 'scene', 'generation', 'ai']
  });

  // 注册场景理解节点
  registry.registerNodeType({
    type: 'nlp/scene/understand',
    category: NodeCategory.AI,
    constructor: SceneUnderstandingNode,
    label: '场景理解',
    description: '理解自然语言中的场景描述',
    icon: 'eye',
    color: '#722ED1',
    tags: ['nlp', 'understanding', 'analysis']
  });

  console.log('已注册自然语言场景节点');
}
```

## 6. 部署配置与环境设置

### 6.1 Docker配置

创建专门的NLP场景生成服务Docker配置：

```dockerfile
# server/nlp-scene-service/Dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装Python和AI模型依赖
RUN apk add --no-cache python3 py3-pip

# 复制package文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3009

# 启动命令
CMD ["npm", "run", "start:prod"]
```

### 6.2 Docker Compose配置

```yaml
# docker-compose.nlp.yml
version: '3.8'

services:
  nlp-scene-service:
    build:
      context: ./server/nlp-scene-service
      dockerfile: Dockerfile
    ports:
      - "3009:3009"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=****************************************/dlengine
      - REDIS_URL=redis://redis:6379
      - AI_MODEL_ENDPOINT=http://ai-model-service:8000
      - SCENE_STORAGE_PATH=/app/storage/scenes
    volumes:
      - scene_storage:/app/storage/scenes
    depends_on:
      - postgres
      - redis
      - ai-model-service
    networks:
      - dlengine-network

  ai-model-service:
    image: tensorflow/serving:latest
    ports:
      - "8000:8501"
    environment:
      - MODEL_NAME=nlp_scene_model
    volumes:
      - ./ai-models:/models
    networks:
      - dlengine-network

volumes:
  scene_storage:

networks:
  dlengine-network:
    external: true
```

### 6.3 Kubernetes部署配置

```yaml
# k8s/nlp-scene-service.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nlp-scene-service
  namespace: dlengine
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nlp-scene-service
  template:
    metadata:
      labels:
        app: nlp-scene-service
    spec:
      containers:
      - name: nlp-scene-service
        image: dlengine/nlp-scene-service:latest
        ports:
        - containerPort: 3009
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        - name: AI_MODEL_ENDPOINT
          value: "http://ai-model-service:8000"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3009
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3009
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: nlp-scene-service
  namespace: dlengine
spec:
  selector:
    app: nlp-scene-service
  ports:
  - protocol: TCP
    port: 3009
    targetPort: 3009
  type: ClusterIP
```

## 7. 测试方案

### 7.1 单元测试

```typescript
// server/nlp-scene-service/src/services/__tests__/NLPSceneService.test.ts
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NLPSceneService } from '../NLPSceneService';
import { GeneratedScene } from '../../entities/GeneratedScene';
import { SceneTemplate } from '../../entities/SceneTemplate';
import { AIModelService } from '../AIModelService';
import { SceneStorageService } from '../SceneStorageService';

describe('NLPSceneService', () => {
  let service: NLPSceneService;
  let generatedSceneRepository: Repository<GeneratedScene>;
  let aiModelService: AIModelService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NLPSceneService,
        {
          provide: getRepositoryToken(GeneratedScene),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(SceneTemplate),
          useClass: Repository,
        },
        {
          provide: AIModelService,
          useValue: {
            understandText: jest.fn(),
            generateSceneStructure: jest.fn(),
            generate3DContent: jest.fn(),
          },
        },
        {
          provide: SceneStorageService,
          useValue: {
            saveScene: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<NLPSceneService>(NLPSceneService);
    generatedSceneRepository = module.get<Repository<GeneratedScene>>(
      getRepositoryToken(GeneratedScene),
    );
    aiModelService = module.get<AIModelService>(AIModelService);
  });

  describe('generateScene', () => {
    it('应该成功生成场景', async () => {
      // 模拟数据
      const request = {
        text: '创建一个现代办公室',
        style: 'realistic',
        quality: 80,
        maxObjects: 50,
        userId: 'user123',
      };

      const mockUnderstanding = {
        entities: [{ text: '办公室', type: 'LOCATION' }],
        sentiment: 'neutral',
        intent: 'CREATE_SCENE',
      };

      const mockSceneContent = {
        entities: [{ id: '1', type: 'desk' }],
        environment: { lighting: 'natural' },
      };

      // 设置模拟返回值
      jest.spyOn(aiModelService, 'understandText').mockResolvedValue(mockUnderstanding);
      jest.spyOn(aiModelService, 'generateSceneStructure').mockResolvedValue({});
      jest.spyOn(aiModelService, 'generate3DContent').mockResolvedValue(mockSceneContent);
      jest.spyOn(generatedSceneRepository, 'save').mockResolvedValue({} as any);

      // 执行测试
      const result = await service.generateScene(request);

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.sceneData).toEqual(mockSceneContent);
      expect(aiModelService.understandText).toHaveBeenCalledWith(request.text);
    });

    it('应该处理生成失败的情况', async () => {
      const request = {
        text: '创建一个办公室',
        style: 'realistic',
        quality: 80,
        maxObjects: 50,
        userId: 'user123',
      };

      jest.spyOn(aiModelService, 'understandText').mockRejectedValue(
        new Error('AI服务不可用')
      );

      await expect(service.generateScene(request)).rejects.toThrow('AI服务不可用');
    });
  });
});
```

### 7.2 集成测试

```typescript
// tests/integration/nlp-scene-generation.test.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('NLP场景生成集成测试', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // 获取认证令牌
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        username: 'testuser',
        password: 'testpass',
      });

    authToken = loginResponse.body.accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/api/v1/nlp-scene/generate (POST)', () => {
    it('应该成功生成场景', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/nlp-scene/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          text: '创建一个现代化的会议室，包含长桌和舒适的椅子',
          style: 'realistic',
          quality: 70,
          maxObjects: 30,
          userId: 'test-user-id',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.sceneData).toBeDefined();
      expect(response.body.understanding).toBeDefined();
    });

    it('应该拒绝无效的请求', async () => {
      await request(app.getHttpServer())
        .post('/api/v1/nlp-scene/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          text: '', // 空文本
          style: 'realistic',
          quality: 70,
          maxObjects: 30,
          userId: 'test-user-id',
        })
        .expect(400);
    });
  });

  describe('/api/v1/nlp-scene/preview (POST)', () => {
    it('应该成功生成预览', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/v1/nlp-scene/preview')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          text: '创建一个简单的客厅',
          style: 'minimalist',
          lowQuality: true,
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.previewData).toBeDefined();
    });
  });
});
```

### 7.3 端到端测试

```typescript
// tests/e2e/nlp-scene-workflow.test.ts
import { test, expect } from '@playwright/test';

test.describe('自然语言场景生成工作流', () => {
  test.beforeEach(async ({ page }) => {
    // 登录到编辑器
    await page.goto('/editor');
    await page.fill('[data-testid="username"]', 'testuser');
    await page.fill('[data-testid="password"]', 'testpass');
    await page.click('[data-testid="login-button"]');

    // 等待编辑器加载
    await page.waitForSelector('[data-testid="editor-main"]');
  });

  test('完整的场景生成流程', async ({ page }) => {
    // 1. 打开自然语言场景生成面板
    await page.click('[data-testid="panel-nlp-scene-generation"]');
    await page.waitForSelector('[data-testid="nlp-scene-panel"]');

    // 2. 输入场景描述
    await page.fill(
      '[data-testid="scene-description-input"]',
      '创建一个温馨的咖啡厅，有木质桌椅、暖色灯光和绿色植物'
    );

    // 3. 设置生成参数
    await page.selectOption('[data-testid="style-select"]', 'realistic');
    await page.fill('[data-testid="quality-slider"]', '80');

    // 4. 生成场景
    await page.click('[data-testid="generate-scene-button"]');

    // 5. 等待生成完成
    await page.waitForSelector('[data-testid="generation-progress"]');
    await page.waitForSelector('[data-testid="generation-complete"]', { timeout: 60000 });

    // 6. 验证场景已生成
    const scenePreview = page.locator('[data-testid="scene-preview"]');
    await expect(scenePreview).toBeVisible();

    // 7. 检查生成历史
    const historyItems = page.locator('[data-testid="history-item"]');
    await expect(historyItems).toHaveCount(1);

    // 8. 预览场景
    await page.click('[data-testid="preview-scene-button"]');
    const previewModal = page.locator('[data-testid="scene-preview-modal"]');
    await expect(previewModal).toBeVisible();

    // 9. 保存场景
    await page.click('[data-testid="save-scene-button"]');
    await page.fill('[data-testid="scene-name-input"]', '温馨咖啡厅');
    await page.click('[data-testid="confirm-save-button"]');

    // 10. 验证保存成功
    const successMessage = page.locator('[data-testid="save-success-message"]');
    await expect(successMessage).toBeVisible();
  });

  test('错误处理和重试机制', async ({ page }) => {
    // 测试网络错误情况
    await page.route('**/api/v1/nlp-scene/generate', route => {
      route.abort('failed');
    });

    await page.click('[data-testid="panel-nlp-scene-generation"]');
    await page.fill('[data-testid="scene-description-input"]', '创建一个办公室');
    await page.click('[data-testid="generate-scene-button"]');

    // 验证错误提示
    const errorMessage = page.locator('[data-testid="error-message"]');
    await expect(errorMessage).toBeVisible();

    // 测试重试功能
    await page.unroute('**/api/v1/nlp-scene/generate');
    await page.click('[data-testid="retry-button"]');

    // 验证重试成功
    await page.waitForSelector('[data-testid="generation-complete"]', { timeout: 60000 });
  });
});
```

## 8. 使用示例和最佳实践

### 8.1 基础使用示例

```typescript
// examples/nlp-scene-generation/basic-usage.ts
import { Engine } from '@dl-engine/core';
import { NLPSceneGenerator } from '@dl-engine/ai';

async function basicSceneGeneration() {
  // 1. 初始化引擎
  const engine = new Engine();
  await engine.initialize();

  // 2. 获取自然语言场景生成器
  const nlpGenerator = engine.getSystem('NLPSceneGenerator');

  // 3. 生成场景
  const scene = await nlpGenerator.generateSceneFromNaturalLanguage(
    '创建一个现代化的图书馆，有高大的书架、舒适的阅读区和自然光照',
    {
      style: 'realistic',
      constraints: {
        maxObjects: 50,
        maxPolygons: 100000,
        targetFrameRate: 60
      },
      onProgress: (progress) => {
        console.log(`生成进度: ${progress}%`);
      }
    }
  );

  // 4. 将场景添加到世界中
  const world = engine.getWorld();
  world.addScene(scene);

  console.log('场景生成完成!');
}

basicSceneGeneration().catch(console.error);
```

### 8.2 高级使用示例

```typescript
// examples/nlp-scene-generation/advanced-usage.ts
import { Engine } from '@dl-engine/core';
import { NLPSceneGenerator } from '@dl-engine/ai';
import { SceneOptimizer } from '@dl-engine/optimization';

class AdvancedSceneGenerator {
  private engine: Engine;
  private nlpGenerator: NLPSceneGenerator;
  private optimizer: SceneOptimizer;

  constructor() {
    this.engine = new Engine();
    this.optimizer = new SceneOptimizer();
  }

  async initialize() {
    await this.engine.initialize();
    this.nlpGenerator = this.engine.getSystem('NLPSceneGenerator');

    // 配置生成器
    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.nlpGenerator.addEventListener('sceneGenerated', (event) => {
      console.log('场景生成完成:', event.scene.name);
      this.optimizeScene(event.scene);
    });

    this.nlpGenerator.addEventListener('generationError', (event) => {
      console.error('场景生成失败:', event.error);
      this.handleGenerationError(event);
    });
  }

  async generateMultipleScenes(descriptions: string[]) {
    const scenes = [];

    for (const description of descriptions) {
      try {
        const scene = await this.generateSceneWithRetry(description);
        scenes.push(scene);
      } catch (error) {
        console.error(`生成场景失败: ${description}`, error);
      }
    }

    return scenes;
  }

  private async generateSceneWithRetry(
    description: string,
    maxRetries: number = 3
  ) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.nlpGenerator.generateSceneFromNaturalLanguage(
          description,
          {
            style: this.inferOptimalStyle(description),
            constraints: this.calculateOptimalConstraints(description)
          }
        );
      } catch (error) {
        if (attempt === maxRetries) throw error;

        console.warn(`生成尝试 ${attempt} 失败，重试中...`);
        await this.delay(1000 * attempt); // 指数退避
      }
    }
  }

  private inferOptimalStyle(description: string): string {
    const keywords = {
      realistic: ['现实', '真实', '写实', '逼真'],
      cartoon: ['卡通', '动画', '可爱', '童话'],
      scifi: ['科幻', '未来', '太空', '机器人'],
      fantasy: ['奇幻', '魔法', '龙', '城堡'],
      minimalist: ['简约', '极简', '现代', '简洁']
    };

    for (const [style, words] of Object.entries(keywords)) {
      if (words.some(word => description.includes(word))) {
        return style;
      }
    }

    return 'realistic';
  }

  private calculateOptimalConstraints(description: string) {
    const complexity = this.estimateComplexity(description);

    return {
      maxObjects: Math.min(100, complexity * 10),
      maxPolygons: Math.min(200000, complexity * 20000),
      targetFrameRate: complexity > 5 ? 30 : 60
    };
  }

  private estimateComplexity(description: string): number {
    const complexityIndicators = [
      '复杂', '详细', '精细', '丰富', '多样',
      '大型', '巨大', '宏伟', '壮观'
    ];

    let complexity = 1;
    complexityIndicators.forEach(indicator => {
      if (description.includes(indicator)) complexity++;
    });

    return Math.min(complexity, 10);
  }

  private async optimizeScene(scene: any) {
    // 性能优化
    await this.optimizer.optimizeForPerformance(scene);

    // 视觉质量优化
    await this.optimizer.optimizeForQuality(scene);

    console.log('场景优化完成');
  }

  private handleGenerationError(event: any) {
    // 错误分析和处理
    const errorType = this.analyzeError(event.error);

    switch (errorType) {
      case 'network':
        console.log('网络错误，建议检查连接');
        break;
      case 'resource':
        console.log('资源不足，建议降低质量设置');
        break;
      case 'input':
        console.log('输入无效，建议修改描述');
        break;
      default:
        console.log('未知错误，请联系技术支持');
    }
  }

  private analyzeError(error: Error): string {
    if (error.message.includes('network') || error.message.includes('timeout')) {
      return 'network';
    }
    if (error.message.includes('memory') || error.message.includes('resource')) {
      return 'resource';
    }
    if (error.message.includes('invalid') || error.message.includes('empty')) {
      return 'input';
    }
    return 'unknown';
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 使用示例
async function advancedExample() {
  const generator = new AdvancedSceneGenerator();
  await generator.initialize();

  const descriptions = [
    '创建一个宁静的日式庭院，有竹子、石灯笼和小桥流水',
    '设计一个现代化的科技展厅，展示最新的VR设备和全息投影',
    '构建一个中世纪的城堡大厅，有高大的石柱、彩色玻璃窗和壁炉'
  ];

  const scenes = await generator.generateMultipleScenes(descriptions);
  console.log(`成功生成 ${scenes.length} 个场景`);
}

advancedExample().catch(console.error);
```

## 9. 性能优化与监控

### 9.1 性能优化策略

```typescript
// engine/src/ai/optimization/SceneGenerationOptimizer.ts
export class SceneGenerationOptimizer {
  private performanceMetrics: Map<string, number> = new Map();
  private cacheManager: CacheManager;

  constructor() {
    this.cacheManager = new CacheManager({
      maxSize: 100,
      ttl: 3600000 // 1小时
    });
  }

  /**
   * 优化生成请求
   */
  async optimizeGenerationRequest(request: any): Promise<any> {
    // 1. 检查缓存
    const cacheKey = this.generateCacheKey(request);
    const cached = await this.cacheManager.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 2. 动态调整参数
    const optimizedRequest = this.adjustParameters(request);

    // 3. 负载均衡
    const endpoint = await this.selectOptimalEndpoint();
    optimizedRequest.endpoint = endpoint;

    return optimizedRequest;
  }

  /**
   * 批量生成优化
   */
  async optimizeBatchGeneration(requests: any[]): Promise<any[]> {
    // 1. 请求分组
    const groups = this.groupSimilarRequests(requests);

    // 2. 并行处理
    const results = await Promise.all(
      groups.map(group => this.processBatch(group))
    );

    return results.flat();
  }

  /**
   * 实时性能监控
   */
  startPerformanceMonitoring(): void {
    setInterval(() => {
      this.collectMetrics();
      this.adjustOptimizationStrategy();
    }, 30000); // 每30秒监控一次
  }

  private generateCacheKey(request: any): string {
    return `scene_${request.text}_${request.style}_${request.quality}`;
  }

  private adjustParameters(request: any): any {
    const currentLoad = this.getCurrentSystemLoad();

    if (currentLoad > 0.8) {
      // 高负载时降低质量
      request.quality = Math.max(request.quality * 0.7, 30);
      request.maxObjects = Math.max(request.maxObjects * 0.8, 10);
    }

    return request;
  }

  private async selectOptimalEndpoint(): Promise<string> {
    const endpoints = await this.getAvailableEndpoints();

    // 选择负载最低的端点
    return endpoints.reduce((best, current) =>
      current.load < best.load ? current : best
    ).url;
  }

  private groupSimilarRequests(requests: any[]): any[][] {
    const groups: Map<string, any[]> = new Map();

    requests.forEach(request => {
      const key = `${request.style}_${Math.floor(request.quality / 20)}`;
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(request);
    });

    return Array.from(groups.values());
  }

  private async processBatch(batch: any[]): Promise<any[]> {
    // 批量处理逻辑
    return Promise.all(batch.map(request => this.processRequest(request)));
  }

  private collectMetrics(): void {
    // 收集性能指标
    const metrics = {
      responseTime: this.getAverageResponseTime(),
      throughput: this.getThroughput(),
      errorRate: this.getErrorRate(),
      cacheHitRate: this.getCacheHitRate()
    };

    this.performanceMetrics.set('current', metrics);
  }

  private adjustOptimizationStrategy(): void {
    const metrics = this.performanceMetrics.get('current');

    if (metrics.responseTime > 10000) { // 超过10秒
      this.enableAggressiveOptimization();
    } else if (metrics.responseTime < 3000) { // 小于3秒
      this.enableQualityOptimization();
    }
  }
}
```

### 9.2 监控和日志系统

```typescript
// server/nlp-scene-service/src/monitoring/MetricsCollector.ts
import { Injectable } from '@nestjs/common';
import { Gauge, Counter, Histogram, register } from 'prom-client';

@Injectable()
export class MetricsCollector {
  private readonly sceneGenerationDuration: Histogram<string>;
  private readonly sceneGenerationTotal: Counter<string>;
  private readonly activeGenerations: Gauge<string>;
  private readonly errorTotal: Counter<string>;

  constructor() {
    this.sceneGenerationDuration = new Histogram({
      name: 'nlp_scene_generation_duration_seconds',
      help: '场景生成耗时',
      labelNames: ['style', 'quality_level'],
      buckets: [1, 5, 10, 30, 60, 120, 300]
    });

    this.sceneGenerationTotal = new Counter({
      name: 'nlp_scene_generation_total',
      help: '场景生成总数',
      labelNames: ['style', 'status']
    });

    this.activeGenerations = new Gauge({
      name: 'nlp_scene_active_generations',
      help: '当前活跃的场景生成任务数'
    });

    this.errorTotal = new Counter({
      name: 'nlp_scene_errors_total',
      help: '场景生成错误总数',
      labelNames: ['error_type']
    });

    register.registerMetric(this.sceneGenerationDuration);
    register.registerMetric(this.sceneGenerationTotal);
    register.registerMetric(this.activeGenerations);
    register.registerMetric(this.errorTotal);
  }

  recordGenerationStart(style: string): () => void {
    this.activeGenerations.inc();
    const endTimer = this.sceneGenerationDuration.startTimer({
      style,
      quality_level: 'medium'
    });

    return () => {
      endTimer();
      this.activeGenerations.dec();
    };
  }

  recordGenerationSuccess(style: string): void {
    this.sceneGenerationTotal.inc({ style, status: 'success' });
  }

  recordGenerationError(style: string, errorType: string): void {
    this.sceneGenerationTotal.inc({ style, status: 'error' });
    this.errorTotal.inc({ error_type: errorType });
  }

  getMetrics(): string {
    return register.metrics();
  }
}
```

### 9.3 健康检查和自动恢复

```typescript
// server/nlp-scene-service/src/health/HealthController.ts
import { Controller, Get } from '@nestjs/common';
import { HealthCheck, HealthCheckService, TypeOrmHealthIndicator } from '@nestjs/terminus';
import { AIModelHealthIndicator } from './AIModelHealthIndicator';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private aiModel: AIModelHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.aiModel.isHealthy('ai-model'),
    ]);
  }

  @Get('ready')
  @HealthCheck()
  readiness() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.aiModel.isReady('ai-model'),
    ]);
  }
}
```

## 10. 部署和运维

### 10.1 CI/CD流水线

```yaml
# .github/workflows/nlp-scene-service.yml
name: NLP Scene Service CI/CD

on:
  push:
    branches: [ main, develop ]
    paths: [ 'server/nlp-scene-service/**' ]
  pull_request:
    branches: [ main ]
    paths: [ 'server/nlp-scene-service/**' ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: server/nlp-scene-service/package-lock.json

    - name: Install dependencies
      run: |
        cd server/nlp-scene-service
        npm ci

    - name: Run tests
      run: |
        cd server/nlp-scene-service
        npm run test:unit
        npm run test:integration

    - name: Run E2E tests
      run: |
        cd server/nlp-scene-service
        npm run test:e2e

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Build Docker image
      run: |
        cd server/nlp-scene-service
        docker build -t dlengine/nlp-scene-service:${{ github.sha }} .
        docker tag dlengine/nlp-scene-service:${{ github.sha }} dlengine/nlp-scene-service:latest

    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push dlengine/nlp-scene-service:${{ github.sha }}
        docker push dlengine/nlp-scene-service:latest

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Deploy to Kubernetes
      run: |
        kubectl set image deployment/nlp-scene-service nlp-scene-service=dlengine/nlp-scene-service:${{ github.sha }}
        kubectl rollout status deployment/nlp-scene-service
```

### 10.2 监控配置

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'nlp-scene-service'
    static_configs:
      - targets: ['nlp-scene-service:3009']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'ai-model-service'
    static_configs:
      - targets: ['ai-model-service:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

rule_files:
  - "nlp_scene_alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

```yaml
# monitoring/nlp_scene_alerts.yml
groups:
- name: nlp_scene_alerts
  rules:
  - alert: HighSceneGenerationLatency
    expr: histogram_quantile(0.95, nlp_scene_generation_duration_seconds) > 30
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "场景生成延迟过高"
      description: "95%的场景生成请求耗时超过30秒"

  - alert: HighErrorRate
    expr: rate(nlp_scene_errors_total[5m]) > 0.1
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "场景生成错误率过高"
      description: "场景生成错误率超过10%"

  - alert: AIModelDown
    expr: up{job="ai-model-service"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "AI模型服务不可用"
      description: "AI模型服务已停止响应"
```

## 11. 总结

### 11.1 实现成果

本方案成功设计并实现了完整的自然语言生成场景功能，包括：

1. **编辑器界面层**：
   - 用户友好的自然语言输入界面
   - 实时场景预览和参数配置
   - 生成历史管理和场景保存功能

2. **引擎核心层**：
   - 强大的自然语言理解和处理能力
   - 高效的3D场景生成算法
   - 智能的场景优化和后处理

3. **服务器端层**：
   - 可扩展的微服务架构
   - 完善的API接口和数据管理
   - 高性能的AI模型服务集成

4. **视觉脚本层**：
   - 丰富的自然语言处理节点
   - 灵活的可视化编程支持
   - 完整的节点生态系统

### 11.2 技术优势

- **智能化程度高**：深度集成AI技术，实现真正的自然语言理解
- **性能表现优秀**：多层次优化确保高效的生成性能
- **扩展性强**：模块化设计支持功能的灵活扩展
- **用户体验佳**：直观的界面设计和流畅的交互体验

### 11.3 应用价值

- **降低创作门槛**：让非专业用户也能轻松创建复杂3D场景
- **提高工作效率**：大幅缩短从概念到实现的时间
- **激发创意灵感**：通过AI辅助激发用户的创作潜能
- **标准化流程**：建立标准化的内容创作工作流

### 11.4 未来发展

该功能为DL引擎奠定了强大的AI驱动内容生成基础，未来可以进一步扩展到：
- 更多模态的内容生成（音频、动画、交互）
- 更智能的场景理解和推荐
- 更丰富的创作工具和模板
- 更深度的用户个性化定制

通过本方案的实施，DL引擎将成为业界领先的智能化3D内容创作平台。
```
```
```
