/**
 * 场景模板实体
 */
import {
  Entity, PrimaryGeneratedColumn, Column,
  CreateDateColumn, UpdateDateColumn, Index
} from 'typeorm';

@Entity('scene_templates')
@Index(['category'])
@Index(['style'])
@Index(['popularity'])
export class SceneTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('varchar', { length: 200 })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('varchar', { length: 50 })
  category: string;

  @Column('varchar', { length: 50 })
  style: string;

  @Column('text')
  templateText: string;

  @Column('json', { nullable: true })
  defaultParameters: any;

  @Column('varchar', { length: 500, nullable: true })
  previewImage: string;

  @Column('int', { default: 0 })
  popularity: number;

  @Column('boolean', { default: true })
  isActive: boolean;

  @Column('varchar', { length: 100, nullable: true })
  createdBy: string;

  @Column('json', { nullable: true })
  tags: string[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
