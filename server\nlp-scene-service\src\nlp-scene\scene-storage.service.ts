/**
 * 场景存储服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import * as path from 'path';

@Injectable()
export class SceneStorageService {
  private readonly logger = new Logger(SceneStorageService.name);
  private readonly storageBasePath: string;
  private readonly baseUrl: string;

  constructor(private configService: ConfigService) {
    this.storageBasePath = this.configService.get<string>('SCENE_STORAGE_PATH') || './storage/scenes';
    this.baseUrl = this.configService.get<string>('SCENE_BASE_URL') || 'http://localhost:3000/scenes';
    this.ensureStorageDirectory();
  }

  /**
   * 保存场景文件
   */
  async saveSceneFile(sceneId: string, sceneData: any): Promise<string> {
    try {
      this.logger.log(`开始保存场景文件: ${sceneId}`);

      // 创建场景目录
      const sceneDir = path.join(this.storageBasePath, sceneId);
      await this.ensureDirectory(sceneDir);

      // 保存场景数据文件
      const sceneFilePath = path.join(sceneDir, 'scene.json');
      await fs.writeFile(sceneFilePath, JSON.stringify(sceneData, null, 2), 'utf8');

      // 生成缩略图（简化实现）
      await this.generateThumbnail(sceneId, sceneData);

      // 生成元数据文件
      await this.generateMetadata(sceneId, sceneData);

      const sceneUrl = `${this.baseUrl}/${sceneId}/scene.json`;

      this.logger.log(`场景文件保存完成: ${sceneUrl}`);
      return sceneUrl;

    } catch (error) {
      this.logger.error(`保存场景文件失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取场景文件
   */
  async getSceneFile(sceneId: string): Promise<any> {
    try {
      const sceneFilePath = path.join(this.storageBasePath, sceneId, 'scene.json');
      const sceneData = await fs.readFile(sceneFilePath, 'utf8');
      return JSON.parse(sceneData);

    } catch (error) {
      this.logger.error(`获取场景文件失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除场景文件
   */
  async deleteSceneFile(sceneId: string): Promise<void> {
    try {
      const sceneDir = path.join(this.storageBasePath, sceneId);
      await fs.rmdir(sceneDir, { recursive: true });

      this.logger.log(`场景文件删除完成: ${sceneId}`);

    } catch (error) {
      this.logger.error(`删除场景文件失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取场景缩略图URL
   */
  getThumbnailUrl(sceneId: string): string {
    return `${this.baseUrl}/${sceneId}/thumbnail.png`;
  }

  /**
   * 获取场景元数据
   */
  async getSceneMetadata(sceneId: string): Promise<any> {
    try {
      const metadataPath = path.join(this.storageBasePath, sceneId, 'metadata.json');
      const metadata = await fs.readFile(metadataPath, 'utf8');
      return JSON.parse(metadata);

    } catch (error) {
      this.logger.error(`获取场景元数据失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 确保存储目录存在
   */
  private async ensureStorageDirectory(): Promise<void> {
    try {
      await this.ensureDirectory(this.storageBasePath);
      this.logger.log(`存储目录已准备: ${this.storageBasePath}`);

    } catch (error) {
      this.logger.error(`创建存储目录失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * 生成缩略图
   */
  private async generateThumbnail(sceneId: string, sceneData: any): Promise<void> {
    try {
      // 这里应该实现实际的缩略图生成逻辑
      // 可以使用Three.js的离线渲染或其他图像生成库
      
      // 简化实现：创建一个占位符图像信息
      const thumbnailInfo = {
        sceneId,
        generatedAt: new Date(),
        entityCount: sceneData.entities?.length || 0,
        placeholder: true
      };

      const thumbnailPath = path.join(this.storageBasePath, sceneId, 'thumbnail.json');
      await fs.writeFile(thumbnailPath, JSON.stringify(thumbnailInfo, null, 2), 'utf8');

      this.logger.log(`缩略图信息生成完成: ${sceneId}`);

    } catch (error) {
      this.logger.error(`生成缩略图失败: ${error.message}`, error.stack);
      // 不抛出错误，缩略图生成失败不应该影响主流程
    }
  }

  /**
   * 生成元数据
   */
  private async generateMetadata(sceneId: string, sceneData: any): Promise<void> {
    try {
      const metadata = {
        sceneId,
        version: '1.0.0',
        createdAt: new Date(),
        entityCount: sceneData.entities?.length || 0,
        estimatedSize: JSON.stringify(sceneData).length,
        format: 'DL-Engine-Scene',
        compatibility: {
          minEngineVersion: '1.0.0',
          features: ['lighting', 'materials', 'entities']
        }
      };

      const metadataPath = path.join(this.storageBasePath, sceneId, 'metadata.json');
      await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2), 'utf8');

      this.logger.log(`元数据生成完成: ${sceneId}`);

    } catch (error) {
      this.logger.error(`生成元数据失败: ${error.message}`, error.stack);
      // 不抛出错误，元数据生成失败不应该影响主流程
    }
  }

  /**
   * 压缩场景数据
   */
  private async compressSceneData(sceneData: any): Promise<Buffer> {
    // 这里可以实现场景数据压缩
    // 例如使用gzip或其他压缩算法
    return Buffer.from(JSON.stringify(sceneData), 'utf8');
  }

  /**
   * 验证场景数据
   */
  private validateSceneData(sceneData: any): boolean {
    // 验证场景数据的基本结构
    if (!sceneData || typeof sceneData !== 'object') {
      return false;
    }

    // 检查必要字段
    if (!sceneData.name || !Array.isArray(sceneData.entities)) {
      return false;
    }

    return true;
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<any> {
    try {
      const scenes = await fs.readdir(this.storageBasePath);
      let totalSize = 0;
      let sceneCount = 0;

      for (const sceneId of scenes) {
        const sceneDir = path.join(this.storageBasePath, sceneId);
        const stats = await fs.stat(sceneDir);
        
        if (stats.isDirectory()) {
          sceneCount++;
          // 计算目录大小（简化实现）
          const files = await fs.readdir(sceneDir);
          for (const file of files) {
            const filePath = path.join(sceneDir, file);
            const fileStats = await fs.stat(filePath);
            totalSize += fileStats.size;
          }
        }
      }

      return {
        sceneCount,
        totalSize,
        averageSize: sceneCount > 0 ? totalSize / sceneCount : 0,
        storageBasePath: this.storageBasePath
      };

    } catch (error) {
      this.logger.error(`获取存储统计失败: ${error.message}`, error.stack);
      return {
        sceneCount: 0,
        totalSize: 0,
        averageSize: 0,
        error: error.message
      };
    }
  }
}
