import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('IndustrialDataService');

  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // 启用CORS
  app.enableCors({
    origin: true,
    credentials: true,
  });

  // 设置全局前缀
  app.setGlobalPrefix('api/v1');

  const port = configService.get<number>('PORT', 3007);
  
  await app.listen(port);
  
  logger.log(`工业数据采集服务已启动，端口: ${port}`);
  logger.log(`服务地址: http://localhost:${port}/api/v1`);
}

bootstrap().catch(error => {
  console.error('启动工业数据采集服务失败:', error);
  process.exit(1);
});
