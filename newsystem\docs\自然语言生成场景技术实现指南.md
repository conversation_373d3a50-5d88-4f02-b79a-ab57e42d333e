# 自然语言生成场景技术实现指南

## 概述

本指南详细说明了如何在DL引擎中实现自然语言生成场景功能的具体技术细节，包括代码实现、配置方法和集成步骤。

## 一、引擎层实现

### 1.1 创建NLPSceneGenerator系统

首先在引擎中创建核心的自然语言场景生成系统：

```typescript
// engine/src/ai/NLPSceneGenerator.ts
import { System } from '../core/System';
import { World } from '../core/World';
import { Scene } from '../scene/Scene';
import { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';

export interface GenerationOptions {
  style: 'realistic' | 'cartoon' | 'minimalist' | 'scifi' | 'fantasy';
  quality: number; // 1-100
  maxObjects: number;
  constraints: {
    maxPolygons: number;
    targetFrameRate: number;
  };
  onProgress?: (progress: number) => void;
}

export interface LanguageUnderstanding {
  entities: Array<{
    text: string;
    type: 'OBJECT' | 'LOCATION' | 'ATTRIBUTE' | 'ACTION';
    confidence: number;
  }>;
  intent: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  keywords: string[];
  style: string;
}

export class NLPSceneGenerator extends System {
  static readonly NAME = 'NLPSceneGenerator';

  private eventEmitter: EventEmitter;
  private cache: Map<string, Scene>;
  private isInitialized: boolean = false;

  constructor() {
    super(350); // 系统优先级
    this.eventEmitter = new EventEmitter();
    this.cache = new Map();
  }

  public initialize(): void {
    if (this.isInitialized) return;

    console.log('初始化自然语言场景生成器...');
    this.isInitialized = true;
  }

  /**
   * 从自然语言生成场景
   */
  public async generateSceneFromNaturalLanguage(
    userInput: string,
    options: GenerationOptions = {}
  ): Promise<Scene> {
    if (!this.isInitialized) {
      throw new Error('NLPSceneGenerator 未初始化');
    }

    try {
      // 生成缓存键
      const cacheKey = this.generateCacheKey(userInput, options);

      // 检查缓存
      if (this.cache.has(cacheKey)) {
        console.log('从缓存返回场景');
        return this.cache.get(cacheKey)!;
      }

      // 第一步：自然语言理解 (20%)
      options.onProgress?.(20);
      const understanding = await this.understandText(userInput);

      // 第二步：场景规划 (40%)
      options.onProgress?.(40);
      const scenePlan = await this.planScene(understanding, options);

      // 第三步：生成3D内容 (80%)
      options.onProgress?.(80);
      const scene = await this.generateScene(scenePlan, options);

      // 第四步：后处理优化 (100%)
      options.onProgress?.(100);
      await this.optimizeScene(scene, understanding);

      // 缓存结果
      this.cache.set(cacheKey, scene);

      // 触发事件
      this.eventEmitter.emit('sceneGenerated', {
        userInput,
        scene,
        understanding,
        options
      });

      return scene;

    } catch (error) {
      console.error('自然语言场景生成失败:', error);
      this.eventEmitter.emit('generationError', { userInput, error });
      throw error;
    }
  }

  /**
   * 理解自然语言文本
   */
  private async understandText(text: string): Promise<LanguageUnderstanding> {
    // 简化的文本理解实现
    const entities = this.extractEntities(text);
    const intent = this.classifyIntent(text);
    const sentiment = this.analyzeSentiment(text);
    const keywords = this.extractKeywords(text);
    const style = this.inferStyle(text);

    return {
      entities,
      intent,
      sentiment,
      keywords,
      style
    };
  }

  /**
   * 提取实体
   */
  private extractEntities(text: string): LanguageUnderstanding['entities'] {
    const entities: LanguageUnderstanding['entities'] = [];

    // 对象实体
    const objectPatterns = [
      /桌子|椅子|沙发|床|柜子|书架/g,
      /电脑|电视|灯|植物|花|树/g,
      /汽车|建筑|房子|门|窗户/g
    ];

    objectPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'OBJECT',
            confidence: 0.8
          });
        });
      }
    });

    // 位置实体
    const locationPatterns = [
      /办公室|客厅|卧室|厨房|浴室/g,
      /学校|图书馆|咖啡厅|餐厅|商店/g,
      /公园|广场|街道|花园/g
    ];

    locationPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'LOCATION',
            confidence: 0.9
          });
        });
      }
    });

    // 属性实体
    const attributePatterns = [
      /现代|古典|简约|豪华|温馨/g,
      /明亮|昏暗|宽敞|狭小|舒适/g,
      /红色|蓝色|绿色|白色|黑色/g
    ];

    attributePatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            text: match,
            type: 'ATTRIBUTE',
            confidence: 0.7
          });
        });
      }
    });

    return entities;
  }

  /**
   * 分类意图
   */
  private classifyIntent(text: string): string {
    if (/创建|建造|制作|生成/.test(text)) return 'CREATE';
    if (/修改|改变|调整|更新/.test(text)) return 'MODIFY';
    if (/删除|移除|清除/.test(text)) return 'DELETE';
    return 'CREATE'; // 默认为创建意图
  }

  /**
   * 分析情感
   */
  private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['美丽', '温馨', '舒适', '明亮', '愉快', '漂亮'];
    const negativeWords = ['阴暗', '破旧', '肮脏', '混乱', '压抑'];

    const positiveCount = positiveWords.filter(word => text.includes(word)).length;
    const negativeCount = negativeWords.filter(word => text.includes(word)).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * 提取关键词
   */
  private extractKeywords(text: string): string[] {
    // 简化的关键词提取
    const words = text.split(/\s+|，|。|、/);
    return words.filter(word => word.length > 1 && !/的|了|在|和|与|或/.test(word));
  }

  /**
   * 推断风格
   */
  private inferStyle(text: string): string {
    if (/科幻|未来|太空|机器人/.test(text)) return 'scifi';
    if (/卡通|可爱|童话|动画/.test(text)) return 'cartoon';
    if (/简约|极简|现代|简洁/.test(text)) return 'minimalist';
    if (/魔法|奇幻|龙|城堡/.test(text)) return 'fantasy';
    return 'realistic';
  }

  /**
   * 规划场景
   */
  private async planScene(
    understanding: LanguageUnderstanding,
    options: GenerationOptions
  ): Promise<any> {
    const plan = {
      layout: this.planLayout(understanding),
      objects: this.planObjects(understanding, options),
      lighting: this.planLighting(understanding),
      materials: this.planMaterials(understanding),
      atmosphere: this.planAtmosphere(understanding)
    };

    return plan;
  }

  /**
   * 规划布局
   */
  private planLayout(understanding: LanguageUnderstanding): any {
    const locations = understanding.entities.filter(e => e.type === 'LOCATION');

    if (locations.length > 0) {
      const location = locations[0].text;

      // 根据位置类型返回不同的布局
      switch (location) {
        case '办公室':
          return {
            type: 'office',
            size: { width: 10, height: 3, depth: 8 },
            zones: ['work_area', 'meeting_area', 'storage_area']
          };
        case '客厅':
          return {
            type: 'living_room',
            size: { width: 12, height: 3, depth: 10 },
            zones: ['seating_area', 'entertainment_area', 'decoration_area']
          };
        case '图书馆':
          return {
            type: 'library',
            size: { width: 20, height: 4, depth: 15 },
            zones: ['reading_area', 'book_storage', 'study_area']
          };
        default:
          return {
            type: 'generic',
            size: { width: 10, height: 3, depth: 10 },
            zones: ['main_area']
          };
      }
    }

    return {
      type: 'generic',
      size: { width: 10, height: 3, depth: 10 },
      zones: ['main_area']
    };
  }

  /**
   * 规划对象
   */
  private planObjects(
    understanding: LanguageUnderstanding,
    options: GenerationOptions
  ): any[] {
    const objects = understanding.entities.filter(e => e.type === 'OBJECT');
    const plannedObjects = [];

    // 限制对象数量
    const maxObjects = Math.min(objects.length, options.maxObjects || 50);

    for (let i = 0; i < maxObjects; i++) {
      const obj = objects[i % objects.length];
      plannedObjects.push({
        type: obj.text,
        position: this.generateRandomPosition(),
        rotation: this.generateRandomRotation(),
        scale: this.generateRandomScale(),
        material: this.selectMaterial(obj.text)
      });
    }

    return plannedObjects;
  }

  /**
   * 生成随机位置
   */
  private generateRandomPosition(): { x: number, y: number, z: number } {
    return {
      x: (Math.random() - 0.5) * 10,
      y: 0,
      z: (Math.random() - 0.5) * 10
    };
  }

  /**
   * 生成随机旋转
   */
  private generateRandomRotation(): { x: number, y: number, z: number } {
    return {
      x: 0,
      y: Math.random() * Math.PI * 2,
      z: 0
    };
  }

  /**
   * 生成随机缩放
   */
  private generateRandomScale(): { x: number, y: number, z: number } {
    const scale = 0.8 + Math.random() * 0.4; // 0.8 到 1.2
    return { x: scale, y: scale, z: scale };
  }

  /**
   * 选择材质
   */
  private selectMaterial(objectType: string): string {
    const materialMap: { [key: string]: string } = {
      '桌子': 'wood',
      '椅子': 'fabric',
      '沙发': 'leather',
      '电脑': 'plastic',
      '植物': 'organic',
      '灯': 'metal'
    };

    return materialMap[objectType] || 'default';
  }

  /**
   * 规划光照
   */
  private planLighting(understanding: LanguageUnderstanding): any {
    const sentiment = understanding.sentiment;
    const attributes = understanding.entities.filter(e => e.type === 'ATTRIBUTE');

    let intensity = 1.0;
    let color = '#ffffff';
    let warmth = 0.5;

    // 根据情感调整光照
    if (sentiment === 'positive') {
      intensity = 1.2;
      warmth = 0.7;
    } else if (sentiment === 'negative') {
      intensity = 0.6;
      warmth = 0.3;
    }

    // 根据属性调整光照
    attributes.forEach(attr => {
      switch (attr.text) {
        case '明亮':
          intensity = 1.5;
          break;
        case '昏暗':
          intensity = 0.4;
          break;
        case '温馨':
          warmth = 0.8;
          color = '#fff8dc';
          break;
      }
    });

    return {
      ambient: {
        intensity: intensity * 0.3,
        color
      },
      directional: {
        intensity: intensity * 0.7,
        color,
        direction: { x: -1, y: -1, z: -1 }
      },
      warmth
    };
  }

  /**
   * 规划材质
   */
  private planMaterials(understanding: LanguageUnderstanding): any[] {
    const style = understanding.style;
    const materials = [];

    switch (style) {
      case 'realistic':
        materials.push(
          { name: 'wood', type: 'physical', roughness: 0.8, metalness: 0.0 },
          { name: 'metal', type: 'physical', roughness: 0.2, metalness: 1.0 },
          { name: 'fabric', type: 'physical', roughness: 0.9, metalness: 0.0 }
        );
        break;
      case 'cartoon':
        materials.push(
          { name: 'toon', type: 'toon', color: '#ff6b6b' },
          { name: 'bright', type: 'basic', color: '#4ecdc4' }
        );
        break;
      default:
        materials.push(
          { name: 'default', type: 'standard', color: '#cccccc' }
        );
    }

    return materials;
  }

  /**
   * 规划氛围
   */
  private planAtmosphere(understanding: LanguageUnderstanding): any {
    return {
      fog: understanding.sentiment === 'negative' ? 0.1 : 0.0,
      skybox: understanding.style === 'scifi' ? 'space' : 'default',
      ambientSound: this.selectAmbientSound(understanding)
    };
  }

  /**
   * 选择环境音效
   */
  private selectAmbientSound(understanding: LanguageUnderstanding): string {
    const locations = understanding.entities.filter(e => e.type === 'LOCATION');

    if (locations.length > 0) {
      const location = locations[0].text;

      switch (location) {
        case '办公室': return 'office_ambient';
        case '咖啡厅': return 'cafe_ambient';
        case '图书馆': return 'library_ambient';
        case '公园': return 'nature_ambient';
        default: return 'default_ambient';
      }
    }

    return 'default_ambient';
  }

  /**
   * 生成场景
   */
  private async generateScene(plan: any, options: GenerationOptions): Promise<Scene> {
    const scene = new Scene();
    scene.name = `Generated Scene ${Date.now()}`;

    // 创建地面
    this.createGround(scene, plan.layout);

    // 创建对象
    plan.objects.forEach((objPlan: any) => {
      this.createObject(scene, objPlan);
    });

    // 设置光照
    this.setupLighting(scene, plan.lighting);

    // 应用材质
    this.applyMaterials(scene, plan.materials);

    // 设置氛围
    this.setupAtmosphere(scene, plan.atmosphere);

    return scene;
  }

  /**
   * 创建地面
   */
  private createGround(scene: Scene, layout: any): void {
    const groundEntity = new Entity();
    groundEntity.name = 'Ground';

    // 这里应该添加实际的地面几何体和材质
    // 简化实现，实际需要根据引擎的具体API来创建

    scene.addEntity(groundEntity);
  }

  /**
   * 创建对象
   */
  private createObject(scene: Scene, objPlan: any): void {
    const entity = new Entity();
    entity.name = objPlan.type;

    // 这里应该根据对象类型创建相应的几何体
    // 简化实现，实际需要根据引擎的具体API来创建

    scene.addEntity(entity);
  }

  /**
   * 设置光照
   */
  private setupLighting(scene: Scene, lighting: any): void {
    // 创建环境光
    const ambientEntity = new Entity();
    ambientEntity.name = 'AmbientLight';
    scene.addEntity(ambientEntity);

    // 创建方向光
    const directionalEntity = new Entity();
    directionalEntity.name = 'DirectionalLight';
    scene.addEntity(directionalEntity);
  }

  /**
   * 应用材质
   */
  private applyMaterials(scene: Scene, materials: any[]): void {
    // 实现材质应用逻辑
  }

  /**
   * 设置氛围
   */
  private setupAtmosphere(scene: Scene, atmosphere: any): void {
    // 实现氛围设置逻辑
  }

  /**
   * 优化场景
   */
  private async optimizeScene(scene: Scene, understanding: LanguageUnderstanding): Promise<void> {
    // 根据情感调整场景
    if (understanding.sentiment === 'positive') {
      this.enhanceBrightness(scene);
    } else if (understanding.sentiment === 'negative') {
      this.reduceBrightness(scene);
    }

    // 性能优化
    await this.optimizePerformance(scene);
  }

  /**
   * 增强亮度
   */
  private enhanceBrightness(scene: Scene): void {
    // 实现亮度增强逻辑
  }

  /**
   * 降低亮度
   */
  private reduceBrightness(scene: Scene): void {
    // 实现亮度降低逻辑
  }

  /**
   * 性能优化
   */
  private async optimizePerformance(scene: Scene): Promise<void> {
    // 实现性能优化逻辑
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(userInput: string, options: GenerationOptions): string {
    return `${userInput}_${options.style}_${options.quality}_${options.maxObjects}`;
  }

  /**
   * 添加事件监听器
   */
  public addEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   */
  public removeEventListener(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
```

### 1.2 注册系统到引擎

在引擎初始化时注册NLPSceneGenerator系统：

```typescript
// engine/src/core/Engine.ts
import { NLPSceneGenerator } from '../ai/NLPSceneGenerator';

export class Engine extends EventEmitter {
  // ... 其他代码

  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // ... 其他初始化代码

    // 添加自然语言场景生成系统
    this.addSystem(new NLPSceneGenerator());

    // ... 其他初始化代码

    this.initialized = true;
    this.emit('initialized');
  }
}
```

## 二、编辑器界面实现

### 2.1 创建自然语言场景生成面板

```typescript
// editor/src/components/panels/NLPSceneGenerationPanel.tsx
import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Card, Input, Button, Select, Slider, Space, Typography,
  Divider, Progress, Alert, List, Tag, Tooltip, Row, Col,
  Modal, message
} from 'antd';
import {
  SendOutlined, HistoryOutlined, SettingOutlined,
  EyeOutlined, SaveOutlined, ReloadOutlined, DeleteOutlined
} from '@ant-design/icons';
import { useEngineService } from '../../hooks/useEngineService';
import { ScenePreview } from '../nlp/ScenePreview';
import { GenerationHistory } from '../nlp/GenerationHistory';

const { TextArea } = Input;
const { Text } = Typography;

interface GenerationOptions {
  style: string;
  quality: number;
  maxObjects: number;
}

interface GenerationRecord {
  id: string;
  text: string;
  style: string;
  quality: number;
  maxObjects: number;
  timestamp: Date;
  scene?: any;
  understanding?: any;
}

export const NLPSceneGenerationPanel: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generatedScene, setGeneratedScene] = useState<any>(null);
  const [generationHistory, setGenerationHistory] = useState<GenerationRecord[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [showHistory, setShowHistory] = useState(false);

  const [options, setOptions] = useState<GenerationOptions>({
    style: 'realistic',
    quality: 80,
    maxObjects: 50
  });

  const engineService = useEngineService();

  // 生成场景
  const handleGenerateScene = useCallback(async () => {
    if (!inputText.trim()) {
      message.warning('请输入场景描述');
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);
    setGeneratedScene(null);

    try {
      const engine = engineService.getEngine();
      const nlpGenerator = engine?.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      const generationOptions = {
        style: options.style,
        quality: options.quality,
        maxObjects: options.maxObjects,
        constraints: {
          maxPolygons: options.quality * 1000,
          targetFrameRate: 60
        },
        onProgress: (progress: number) => {
          setGenerationProgress(progress);
        }
      };

      const scene = await nlpGenerator.generateSceneFromNaturalLanguage(
        inputText,
        generationOptions
      );

      setGeneratedScene(scene);

      // 添加到历史记录
      const historyItem: GenerationRecord = {
        id: Date.now().toString(),
        text: inputText,
        style: options.style,
        quality: options.quality,
        maxObjects: options.maxObjects,
        timestamp: new Date(),
        scene
      };

      setGenerationHistory(prev => [historyItem, ...prev.slice(0, 9)]);

      message.success('场景生成成功！');

    } catch (error) {
      console.error('场景生成失败:', error);
      message.error(`场景生成失败: ${error.message}`);
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  }, [inputText, options, engineService]);

  // 预览场景
  const handlePreviewScene = useCallback(() => {
    if (generatedScene) {
      setShowPreview(true);
    }
  }, [generatedScene]);

  // 保存场景
  const handleSaveScene = useCallback(async () => {
    if (!generatedScene) return;

    try {
      // 这里应该调用场景保存服务
      message.success('场景保存成功！');
    } catch (error) {
      message.error('场景保存失败');
    }
  }, [generatedScene]);

  // 清空历史
  const handleClearHistory = useCallback(() => {
    Modal.confirm({
      title: '确认清空历史记录？',
      content: '此操作不可撤销',
      onOk: () => {
        setGenerationHistory([]);
        message.success('历史记录已清空');
      }
    });
  }, []);

  return (
    <div className="nlp-scene-generation-panel" style={{ padding: '16px' }}>
      {/* 主生成面板 */}
      <Card title="自然语言场景生成" size="small">
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 文本输入区域 */}
          <div>
            <Text strong>场景描述</Text>
            <TextArea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="请描述您想要创建的场景，例如：创建一个现代化的办公室，包含玻璃桌子、舒适的椅子和绿色植物..."
              rows={4}
              maxLength={500}
              showCount
              disabled={isGenerating}
            />
          </div>

          {/* 生成参数配置 */}
          <Card size="small" title="生成参数" type="inner">
            <Row gutter={16}>
              <Col span={24}>
                <Text>风格:</Text>
                <Select
                  value={options.style}
                  onChange={(value) => setOptions(prev => ({ ...prev, style: value }))}
                  style={{ width: '100%', marginTop: 4 }}
                  disabled={isGenerating}
                  options={[
                    { label: '写实风格', value: 'realistic' },
                    { label: '卡通风格', value: 'cartoon' },
                    { label: '简约风格', value: 'minimalist' },
                    { label: '科幻风格', value: 'scifi' },
                    { label: '奇幻风格', value: 'fantasy' }
                  ]}
                />
              </Col>
            </Row>

            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Text>质量等级: {options.quality}</Text>
                <Slider
                  value={options.quality}
                  onChange={(value) => setOptions(prev => ({ ...prev, quality: value }))}
                  min={20}
                  max={100}
                  step={10}
                  disabled={isGenerating}
                  marks={{
                    20: '低',
                    50: '中',
                    80: '高',
                    100: '极高'
                  }}
                />
              </Col>
              <Col span={12}>
                <Text>最大对象数: {options.maxObjects}</Text>
                <Slider
                  value={options.maxObjects}
                  onChange={(value) => setOptions(prev => ({ ...prev, maxObjects: value }))}
                  min={10}
                  max={100}
                  step={5}
                  disabled={isGenerating}
                  marks={{
                    10: '10',
                    50: '50',
                    100: '100'
                  }}
                />
              </Col>
            </Row>
          </Card>

          {/* 操作按钮 */}
          <Space>
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleGenerateScene}
              loading={isGenerating}
              disabled={!inputText.trim()}
            >
              {isGenerating ? '生成中...' : '生成场景'}
            </Button>

            <Button
              icon={<EyeOutlined />}
              onClick={handlePreviewScene}
              disabled={!generatedScene}
            >
              预览
            </Button>

            <Button
              icon={<SaveOutlined />}
              onClick={handleSaveScene}
              disabled={!generatedScene}
            >
              保存
            </Button>

            <Button
              icon={<HistoryOutlined />}
              onClick={() => setShowHistory(!showHistory)}
            >
              历史
            </Button>
          </Space>

          {/* 生成进度 */}
          {isGenerating && (
            <Card size="small" type="inner">
              <Progress
                percent={generationProgress}
                status="active"
                format={(percent) => `生成中... ${percent}%`}
              />
              <Text type="secondary" style={{ fontSize: '12px' }}>
                正在分析语言并生成3D场景...
              </Text>
            </Card>
          )}

          {/* 生成结果提示 */}
          {generatedScene && !isGenerating && (
            <Alert
              message="场景生成完成"
              description="您可以预览、保存或继续编辑生成的场景"
              type="success"
              showIcon
              closable
            />
          )}
        </Space>
      </Card>

      {/* 历史记录面板 */}
      {showHistory && (
        <Card
          title="生成历史"
          size="small"
          style={{ marginTop: 16 }}
          extra={
            <Button
              size="small"
              icon={<DeleteOutlined />}
              onClick={handleClearHistory}
              disabled={generationHistory.length === 0}
            >
              清空
            </Button>
          }
        >
          <GenerationHistory
            history={generationHistory}
            onPreview={(item) => {
              setGeneratedScene(item.scene);
              setShowPreview(true);
            }}
            onRegenerate={(item) => {
              setInputText(item.text);
              setOptions({
                style: item.style,
                quality: item.quality,
                maxObjects: item.maxObjects
              });
            }}
            onDelete={(id) => {
              setGenerationHistory(prev => prev.filter(item => item.id !== id));
            }}
          />
        </Card>
      )}

      {/* 场景预览模态框 */}
      <Modal
        title="场景预览"
        open={showPreview}
        onCancel={() => setShowPreview(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setShowPreview(false)}>
            关闭
          </Button>,
          <Button key="save" type="primary" onClick={handleSaveScene}>
            保存场景
          </Button>
        ]}
      >
        <ScenePreview scene={generatedScene} />
      </Modal>
    </div>
  );
};
```

### 2.2 创建场景预览组件

```typescript
// editor/src/components/nlp/ScenePreview.tsx
import React, { useEffect, useRef, useState } from 'react';
import { Card, Button, Space, Slider, Typography, Switch } from 'antd';
import {
  FullscreenOutlined, RotateLeftOutlined, ZoomInOutlined,
  ZoomOutOutlined, ReloadOutlined
} from '@ant-design/icons';

const { Text } = Typography;

interface ScenePreviewProps {
  scene?: any;
  onFullscreen?: () => void;
}

export const ScenePreview: React.FC<ScenePreviewProps> = ({
  scene,
  onFullscreen
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [cameraDistance, setCameraDistance] = useState(10);
  const [autoRotate, setAutoRotate] = useState(true);
  const [wireframe, setWireframe] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const previewRendererRef = useRef<any>(null);

  useEffect(() => {
    if (!scene || !canvasRef.current) return;

    // 创建预览渲染器
    const initPreviewRenderer = async () => {
      try {
        // 这里应该使用实际的预览渲染器
        // const PreviewRenderer = await import('../../rendering/PreviewRenderer');
        // previewRendererRef.current = new PreviewRenderer.default(canvasRef.current);

        // 简化实现
        const canvas = canvasRef.current!;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          // 绘制简单的预览
          ctx.fillStyle = '#f0f0f0';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          ctx.fillStyle = '#333';
          ctx.font = '16px Arial';
          ctx.textAlign = 'center';
          ctx.fillText('场景预览', canvas.width / 2, canvas.height / 2);
          ctx.fillText(`对象数量: ${scene.entities?.length || 0}`, canvas.width / 2, canvas.height / 2 + 30);
        }

      } catch (error) {
        console.error('预览渲染器初始化失败:', error);
      }
    };

    initPreviewRenderer();

    return () => {
      if (previewRendererRef.current) {
        previewRendererRef.current.dispose?.();
      }
    };
  }, [scene]);

  useEffect(() => {
    if (previewRendererRef.current) {
      previewRendererRef.current.setCameraDistance?.(cameraDistance);
    }
  }, [cameraDistance]);

  useEffect(() => {
    if (previewRendererRef.current) {
      previewRendererRef.current.setAutoRotate?.(autoRotate);
    }
  }, [autoRotate]);

  useEffect(() => {
    if (previewRendererRef.current) {
      previewRendererRef.current.setWireframe?.(wireframe);
    }
  }, [wireframe]);

  const handleReset = () => {
    setCameraDistance(10);
    setAutoRotate(true);
    setWireframe(false);
  };

  if (!scene) {
    return (
      <Card>
        <div style={{
          height: '300px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999'
        }}>
          暂无场景可预览
        </div>
      </Card>
    );
  }

  return (
    <Card
      title="场景预览"
      size="small"
      extra={
        <Space>
          <Button
            size="small"
            icon={<ReloadOutlined />}
            onClick={handleReset}
          >
            重置
          </Button>
          <Button
            size="small"
            icon={<FullscreenOutlined />}
            onClick={onFullscreen}
          >
            全屏
          </Button>
        </Space>
      }
    >
      <div style={{ position: 'relative' }}>
        <canvas
          ref={canvasRef}
          width={600}
          height={300}
          style={{
            width: '100%',
            height: '300px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            display: 'block'
          }}
        />

        {/* 控制面板 */}
        <Card size="small" style={{ marginTop: 8 }} type="inner">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text>相机距离: {cameraDistance}</Text>
              <Slider
                value={cameraDistance}
                onChange={setCameraDistance}
                min={5}
                max={50}
                step={1}
                style={{ marginTop: 4 }}
              />
            </div>

            <Space>
              <Text>自动旋转:</Text>
              <Switch
                checked={autoRotate}
                onChange={setAutoRotate}
                size="small"
              />

              <Text>线框模式:</Text>
              <Switch
                checked={wireframe}
                onChange={setWireframe}
                size="small"
              />

              <Text>显示统计:</Text>
              <Switch
                checked={showStats}
                onChange={setShowStats}
                size="small"
              />
            </Space>
          </Space>
        </Card>

        {/* 统计信息 */}
        {showStats && scene && (
          <Card size="small" style={{ marginTop: 8 }} type="inner">
            <Space direction="vertical" size="small">
              <Text>场景名称: {scene.name || '未命名场景'}</Text>
              <Text>实体数量: {scene.entities?.length || 0}</Text>
              <Text>预估多边形: {(scene.entities?.length || 0) * 1000}</Text>
              <Text>生成时间: {new Date().toLocaleTimeString()}</Text>
            </Space>
          </Card>
        )}
      </div>
    </Card>
  );
};
```

### 2.3 创建生成历史组件

```typescript
// editor/src/components/nlp/GenerationHistory.tsx
import React from 'react';
import { List, Button, Space, Typography, Tag, Tooltip, Popconfirm } from 'antd';
import {
  EyeOutlined, ReloadOutlined, DeleteOutlined,
  ClockCircleOutlined, SettingOutlined
} from '@ant-design/icons';

const { Text } = Typography;

interface GenerationRecord {
  id: string;
  text: string;
  style: string;
  quality: number;
  maxObjects: number;
  timestamp: Date;
  scene?: any;
  understanding?: any;
}

interface GenerationHistoryProps {
  history: GenerationRecord[];
  onPreview: (item: GenerationRecord) => void;
  onRegenerate: (item: GenerationRecord) => void;
  onDelete: (id: string) => void;
}

export const GenerationHistory: React.FC<GenerationHistoryProps> = ({
  history,
  onPreview,
  onRegenerate,
  onDelete
}) => {
  const getStyleColor = (style: string): string => {
    const colorMap: { [key: string]: string } = {
      'realistic': 'blue',
      'cartoon': 'orange',
      'minimalist': 'green',
      'scifi': 'purple',
      'fantasy': 'magenta'
    };
    return colorMap[style] || 'default';
  };

  const getQualityLabel = (quality: number): string => {
    if (quality >= 80) return '高';
    if (quality >= 50) return '中';
    return '低';
  };

  if (history.length === 0) {
    return (
      <div style={{
        textAlign: 'center',
        padding: '40px 20px',
        color: '#999'
      }}>
        <Text type="secondary">暂无生成历史</Text>
      </div>
    );
  }

  return (
    <List
      size="small"
      dataSource={history}
      renderItem={(item) => (
        <List.Item
          actions={[
            <Tooltip title="预览场景">
              <Button
                size="small"
                icon={<EyeOutlined />}
                onClick={() => onPreview(item)}
                disabled={!item.scene}
              />
            </Tooltip>,
            <Tooltip title="重新生成">
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={() => onRegenerate(item)}
              />
            </Tooltip>,
            <Popconfirm
              title="确认删除此记录？"
              onConfirm={() => onDelete(item.id)}
              okText="删除"
              cancelText="取消"
            >
              <Tooltip title="删除记录">
                <Button
                  size="small"
                  icon={<DeleteOutlined />}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          ]}
        >
          <List.Item.Meta
            title={
              <Space>
                <Text
                  ellipsis
                  style={{ maxWidth: 200 }}
                  title={item.text}
                >
                  {item.text}
                </Text>
                <Tag color={getStyleColor(item.style)}>
                  {item.style}
                </Tag>
                <Tag>
                  质量: {getQualityLabel(item.quality)}
                </Tag>
              </Space>
            }
            description={
              <Space size="small">
                <ClockCircleOutlined />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {item.timestamp.toLocaleString()}
                </Text>
                <SettingOutlined />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  对象数: {item.maxObjects}
                </Text>
              </Space>
            }
          />
        </List.Item>
      )}
    />
  );
};
```

### 2.4 注册面板到编辑器

```typescript
// editor/src/core/PanelRegistry.ts
import { NLPSceneGenerationPanel } from '../components/panels/NLPSceneGenerationPanel';

// 在面板注册表中添加自然语言场景生成面板
export function registerNLPScenePanels(registry: PanelRegistry): void {
  registry.registerPanel({
    id: 'nlp-scene-generation',
    name: '自然语言场景生成',
    component: NLPSceneGenerationPanel,
    icon: 'robot',
    category: 'ai',
    defaultPosition: 'left',
    defaultSize: { width: 350, height: 600 },
    resizable: true,
    closable: true,
    description: '使用自然语言描述生成3D场景'
  });
}

// 在编辑器初始化时调用
// editor/src/App.tsx 或相应的初始化文件中
import { registerNLPScenePanels } from './core/PanelRegistry';

// 在应用初始化时注册面板
useEffect(() => {
  const panelRegistry = PanelRegistry.getInstance();
  registerNLPScenePanels(panelRegistry);
}, []);
```

## 三、服务器端实现

### 3.1 创建NLP场景生成服务

```typescript
// server/nlp-scene-service/src/app.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { NLPSceneModule } from './nlp-scene/nlp-scene.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 3306,
      username: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || 'password',
      database: process.env.DB_DATABASE || 'dlengine',
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: process.env.NODE_ENV !== 'production',
    }),
    NLPSceneModule,
  ],
})
export class AppModule {}
```

```typescript
// server/nlp-scene-service/src/nlp-scene/nlp-scene.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { NLPSceneController } from './nlp-scene.controller';
import { NLPSceneService } from './nlp-scene.service';
import { AIModelService } from './ai-model.service';
import { SceneStorageService } from './scene-storage.service';
import { GeneratedScene } from './entities/generated-scene.entity';
import { SceneTemplate } from './entities/scene-template.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([GeneratedScene, SceneTemplate]),
    HttpModule,
  ],
  controllers: [NLPSceneController],
  providers: [NLPSceneService, AIModelService, SceneStorageService],
  exports: [NLPSceneService],
})
export class NLPSceneModule {}
```

### 3.2 创建控制器

```typescript
// server/nlp-scene-service/src/nlp-scene/nlp-scene.controller.ts
import {
  Controller, Post, Get, Body, Param, Query,
  UseGuards, HttpStatus, HttpException
} from '@nestjs/common';
import {
  ApiTags, ApiOperation, ApiResponse,
  ApiBearerAuth, ApiBody, ApiParam
} from '@nestjs/swagger';
import { AuthGuard } from '../guards/auth.guard';
import { RateLimitGuard } from '../guards/rate-limit.guard';
import { NLPSceneService } from './nlp-scene.service';
import {
  GenerateSceneDto, PreviewSceneDto,
  SaveSceneDto, GenerateSceneResponse
} from './dto/nlp-scene.dto';

@ApiTags('自然语言场景生成')
@Controller('api/v1/nlp-scene')
@UseGuards(AuthGuard, RateLimitGuard)
@ApiBearerAuth()
export class NLPSceneController {
  constructor(private readonly nlpSceneService: NLPSceneService) {}

  @Post('generate')
  @ApiOperation({ summary: '生成场景' })
  @ApiBody({ type: GenerateSceneDto })
  @ApiResponse({
    status: 200,
    description: '场景生成成功',
    type: GenerateSceneResponse
  })
  async generateScene(@Body() generateDto: GenerateSceneDto) {
    try {
      const result = await this.nlpSceneService.generateScene(generateDto);
      return {
        success: true,
        data: result,
        message: '场景生成成功'
      };
    } catch (error) {
      throw new HttpException(
        `场景生成失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post('preview')
  @ApiOperation({ summary: '预览场景' })
  @ApiBody({ type: PreviewSceneDto })
  async previewScene(@Body() previewDto: PreviewSceneDto) {
    try {
      const result = await this.nlpSceneService.previewScene(previewDto);
      return {
        success: true,
        data: result,
        message: '预览生成成功'
      };
    } catch (error) {
      throw new HttpException(
        `预览生成失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get('history/:userId')
  @ApiOperation({ summary: '获取用户生成历史' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  async getGenerationHistory(
    @Param('userId') userId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('style') style?: string
  ) {
    try {
      const result = await this.nlpSceneService.getGenerationHistory(
        userId,
        page,
        limit,
        style
      );
      return {
        success: true,
        data: result,
        message: '获取历史记录成功'
      };
    } catch (error) {
      throw new HttpException(
        `获取历史记录失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('save')
  @ApiOperation({ summary: '保存生成的场景' })
  @ApiBody({ type: SaveSceneDto })
  async saveGeneratedScene(@Body() saveDto: SaveSceneDto) {
    try {
      const result = await this.nlpSceneService.saveGeneratedScene(saveDto);
      return {
        success: true,
        data: result,
        message: '场景保存成功'
      };
    } catch (error) {
      throw new HttpException(
        `场景保存失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get('templates')
  @ApiOperation({ summary: '获取场景模板' })
  async getSceneTemplates(
    @Query('category') category?: string,
    @Query('style') style?: string
  ) {
    try {
      const result = await this.nlpSceneService.getSceneTemplates(category, style);
      return {
        success: true,
        data: result,
        message: '获取模板成功'
      };
    } catch (error) {
      throw new HttpException(
        `获取模板失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('stats/:userId')
  @ApiOperation({ summary: '获取用户生成统计' })
  async getUserStats(@Param('userId') userId: string) {
    try {
      const result = await this.nlpSceneService.getUserStats(userId);
      return {
        success: true,
        data: result,
        message: '获取统计信息成功'
      };
    } catch (error) {
      throw new HttpException(
        `获取统计信息失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
```

### 3.3 创建数据传输对象

```typescript
// server/nlp-scene-service/src/nlp-scene/dto/nlp-scene.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsObject, Min, Max } from 'class-validator';

export class GenerateSceneDto {
  @ApiProperty({ description: '场景描述文本' })
  @IsString()
  text: string;

  @ApiProperty({ description: '生成风格', enum: ['realistic', 'cartoon', 'minimalist', 'scifi', 'fantasy'] })
  @IsString()
  style: string;

  @ApiProperty({ description: '质量等级', minimum: 1, maximum: 100 })
  @IsNumber()
  @Min(1)
  @Max(100)
  quality: number;

  @ApiProperty({ description: '最大对象数', minimum: 1, maximum: 100 })
  @IsNumber()
  @Min(1)
  @Max(100)
  maxObjects: number;

  @ApiProperty({ description: '用户ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: '项目ID', required: false })
  @IsOptional()
  @IsString()
  projectId?: string;

  @ApiProperty({ description: '额外约束条件', required: false })
  @IsOptional()
  @IsObject()
  constraints?: {
    maxPolygons?: number;
    targetFrameRate?: number;
  };
}

export class PreviewSceneDto {
  @ApiProperty({ description: '场景描述文本' })
  @IsString()
  text: string;

  @ApiProperty({ description: '生成风格' })
  @IsString()
  style: string;

  @ApiProperty({ description: '是否低质量预览', required: false })
  @IsOptional()
  lowQuality?: boolean;
}

export class SaveSceneDto {
  @ApiProperty({ description: '场景数据' })
  @IsObject()
  sceneData: any;

  @ApiProperty({ description: '场景名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '场景描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '用户ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: '项目ID', required: false })
  @IsOptional()
  @IsString()
  projectId?: string;

  @ApiProperty({ description: '标签', required: false })
  @IsOptional()
  tags?: string[];
}

export class GenerateSceneResponse {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '场景ID' })
  sceneId: string;

  @ApiProperty({ description: '场景URL' })
  sceneUrl?: string;

  @ApiProperty({ description: '场景数据' })
  sceneData: any;

  @ApiProperty({ description: '语言理解结果' })
  understanding: any;

  @ApiProperty({ description: '元数据' })
  metadata: {
    generationTime: number;
    objectCount: number;
    polygonCount: number;
  };
}
```

### 3.4 创建服务实现

```typescript
// server/nlp-scene-service/src/nlp-scene/nlp-scene.service.ts
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { GeneratedScene } from './entities/generated-scene.entity';
import { SceneTemplate } from './entities/scene-template.entity';
import { AIModelService } from './ai-model.service';
import { SceneStorageService } from './scene-storage.service';
import {
  GenerateSceneDto, PreviewSceneDto, SaveSceneDto
} from './dto/nlp-scene.dto';

@Injectable()
export class NLPSceneService {
  private readonly logger = new Logger(NLPSceneService.name);

  constructor(
    @InjectRepository(GeneratedScene)
    private generatedSceneRepository: Repository<GeneratedScene>,
    @InjectRepository(SceneTemplate)
    private sceneTemplateRepository: Repository<SceneTemplate>,
    private aiModelService: AIModelService,
    private sceneStorageService: SceneStorageService
  ) {}

  /**
   * 生成场景
   */
  async generateScene(generateDto: GenerateSceneDto) {
    const startTime = Date.now();

    try {
      this.logger.log(`开始生成场景: ${generateDto.text}`);

      // 1. 自然语言理解
      const understanding = await this.aiModelService.understandText(generateDto.text);
      this.logger.log(`语言理解完成: ${JSON.stringify(understanding)}`);

      // 2. 场景结构生成
      const sceneStructure = await this.aiModelService.generateSceneStructure(
        understanding,
        generateDto.style,
        {
          quality: generateDto.quality,
          maxObjects: generateDto.maxObjects,
          constraints: generateDto.constraints
        }
      );
      this.logger.log(`场景结构生成完成`);

      // 3. 3D内容生成
      const sceneContent = await this.aiModelService.generate3DContent(
        sceneStructure,
        {
          quality: generateDto.quality,
          maxObjects: generateDto.maxObjects,
          ...generateDto.constraints
        }
      );
      this.logger.log(`3D内容生成完成`);

      // 4. 保存生成记录
      const generatedScene = new GeneratedScene();
      generatedScene.inputText = generateDto.text;
      generatedScene.style = generateDto.style;
      generatedScene.quality = generateDto.quality;
      generatedScene.maxObjects = generateDto.maxObjects;
      generatedScene.userId = generateDto.userId;
      generatedScene.projectId = generateDto.projectId;
      generatedScene.sceneData = sceneContent;
      generatedScene.understanding = understanding;
      generatedScene.generationTime = Date.now() - startTime;
      generatedScene.objectCount = sceneContent.entities?.length || 0;
      generatedScene.polygonCount = this.calculatePolygonCount(sceneContent);

      const savedScene = await this.generatedSceneRepository.save(generatedScene);

      // 5. 存储场景文件
      const sceneUrl = await this.sceneStorageService.saveScene(
        sceneContent,
        `scene_${savedScene.id}`
      );

      // 更新场景URL
      savedScene.sceneUrl = sceneUrl;
      await this.generatedSceneRepository.save(savedScene);

      this.logger.log(`场景生成完成: ${savedScene.id}, 耗时: ${generatedScene.generationTime}ms`);

      return {
        sceneId: savedScene.id,
        sceneUrl,
        sceneData: sceneContent,
        understanding,
        metadata: {
          generationTime: generatedScene.generationTime,
          objectCount: generatedScene.objectCount,
          polygonCount: generatedScene.polygonCount
        }
      };

    } catch (error) {
      this.logger.error(`场景生成失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 预览场景
   */
  async previewScene(previewDto: PreviewSceneDto) {
    try {
      this.logger.log(`开始生成预览: ${previewDto.text}`);

      // 使用低质量快速生成预览
      const understanding = await this.aiModelService.understandText(previewDto.text);

      const previewData = await this.aiModelService.generatePreview(
        understanding,
        previewDto.style,
        { lowQuality: previewDto.lowQuality !== false }
      );

      return {
        previewData,
        understanding: {
          entities: understanding.entities,
          sentiment: understanding.sentiment,
          intent: understanding.intent,
          style: understanding.style
        }
      };

    } catch (error) {
      this.logger.error(`场景预览失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取生成历史
   */
  async getGenerationHistory(
    userId: string,
    page: number,
    limit: number,
    style?: string
  ) {
    const where: any = { userId };
    if (style) {
      where.style = style;
    }

    const [items, total] = await this.generatedSceneRepository.findAndCount({
      where,
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
      select: [
        'id', 'inputText', 'style', 'quality', 'maxObjects',
        'createdAt', 'projectId', 'objectCount', 'polygonCount',
        'generationTime', 'sceneUrl'
      ]
    });

    return {
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 保存生成的场景
   */
  async saveGeneratedScene(saveDto: SaveSceneDto) {
    try {
      const sceneUrl = await this.sceneStorageService.saveScene(
        saveDto.sceneData,
        `saved_scene_${Date.now()}`
      );

      // 这里可以保存到项目或用户的场景库中
      // 实际实现可能需要更复杂的逻辑

      return {
        sceneUrl,
        message: '场景保存成功'
      };

    } catch (error) {
      this.logger.error(`场景保存失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取场景模板
   */
  async getSceneTemplates(category?: string, style?: string) {
    const where: any = { isActive: true };

    if (category) {
      where.category = category;
    }

    if (style) {
      where.style = style;
    }

    const templates = await this.sceneTemplateRepository.find({
      where,
      order: { popularity: 'DESC' },
      select: [
        'id', 'name', 'description', 'category', 'style',
        'templateText', 'previewImage', 'popularity'
      ]
    });

    return { templates };
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats(userId: string) {
    const totalGenerated = await this.generatedSceneRepository.count({
      where: { userId }
    });

    const styleStats = await this.generatedSceneRepository
      .createQueryBuilder('scene')
      .select('scene.style', 'style')
      .addSelect('COUNT(*)', 'count')
      .where('scene.userId = :userId', { userId })
      .groupBy('scene.style')
      .getRawMany();

    const avgGenerationTime = await this.generatedSceneRepository
      .createQueryBuilder('scene')
      .select('AVG(scene.generationTime)', 'avgTime')
      .where('scene.userId = :userId', { userId })
      .getRawOne();

    const recentActivity = await this.generatedSceneRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: 5,
      select: ['id', 'inputText', 'style', 'createdAt']
    });

    return {
      totalGenerated,
      styleStats,
      avgGenerationTime: avgGenerationTime?.avgTime || 0,
      recentActivity
    };
  }

  /**
   * 计算多边形数量
   */
  private calculatePolygonCount(sceneData: any): number {
    if (!sceneData.entities) return 0;

    return sceneData.entities.reduce((total: number, entity: any) => {
      return total + (entity.polygonCount || 1000); // 默认每个对象1000个多边形
    }, 0);
  }
}
```

### 3.5 创建数据库实体

```typescript
// server/nlp-scene-service/src/nlp-scene/entities/generated-scene.entity.ts
import {
  Entity, PrimaryGeneratedColumn, Column,
  CreateDateColumn, UpdateDateColumn, Index
} from 'typeorm';

@Entity('generated_scenes')
@Index(['userId', 'createdAt'])
@Index(['style'])
export class GeneratedScene {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('text')
  inputText: string;

  @Column('varchar', { length: 50 })
  style: string;

  @Column('int')
  quality: number;

  @Column('int')
  maxObjects: number;

  @Column('varchar', { length: 100 })
  userId: string;

  @Column('varchar', { length: 100, nullable: true })
  projectId: string;

  @Column('json')
  sceneData: any;

  @Column('json', { nullable: true })
  understanding: any;

  @Column('varchar', { length: 500, nullable: true })
  sceneUrl: string;

  @Column('int', { default: 0 })
  objectCount: number;

  @Column('int', { default: 0 })
  polygonCount: number;

  @Column('int', { default: 0 })
  generationTime: number;

  @Column('boolean', { default: false })
  isPublic: boolean;

  @Column('json', { nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

```typescript
// server/nlp-scene-service/src/nlp-scene/entities/scene-template.entity.ts
import {
  Entity, PrimaryGeneratedColumn, Column,
  CreateDateColumn, UpdateDateColumn, Index
} from 'typeorm';

@Entity('scene_templates')
@Index(['category'])
@Index(['style'])
@Index(['popularity'])
export class SceneTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('varchar', { length: 200 })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column('varchar', { length: 50 })
  category: string;

  @Column('varchar', { length: 50 })
  style: string;

  @Column('text')
  templateText: string;

  @Column('json', { nullable: true })
  defaultParameters: any;

  @Column('varchar', { length: 500, nullable: true })
  previewImage: string;

  @Column('int', { default: 0 })
  popularity: number;

  @Column('boolean', { default: true })
  isActive: boolean;

  @Column('varchar', { length: 100, nullable: true })
  createdBy: string;

  @Column('json', { nullable: true })
  tags: string[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

## 四、视觉脚本集成

### 4.1 创建自然语言场景生成节点

```typescript
// engine/src/visualscript/nodes/NLPSceneGenerationNode.ts
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { GenerationOptions } from '../../ai/NLPSceneGenerator';

export class NLPSceneGenerationNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/scene/generate',
      category: NodeCategory.AI,
      name: '自然语言场景生成',
      description: '基于自然语言描述生成3D场景'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addSocket({
      name: 'text',
      direction: SocketDirection.INPUT,
      type: SocketType.STRING,
      label: '场景描述',
      required: true
    });

    this.addSocket({
      name: 'style',
      direction: SocketDirection.INPUT,
      type: SocketType.STRING,
      label: '风格',
      defaultValue: 'realistic',
      options: [
        { label: '写实', value: 'realistic' },
        { label: '卡通', value: 'cartoon' },
        { label: '简约', value: 'minimalist' },
        { label: '科幻', value: 'scifi' },
        { label: '奇幻', value: 'fantasy' }
      ]
    });

    this.addSocket({
      name: 'quality',
      direction: SocketDirection.INPUT,
      type: SocketType.NUMBER,
      label: '质量等级',
      defaultValue: 80,
      min: 1,
      max: 100
    });

    this.addSocket({
      name: 'maxObjects',
      direction: SocketDirection.INPUT,
      type: SocketType.NUMBER,
      label: '最大对象数',
      defaultValue: 50,
      min: 1,
      max: 100
    });

    // 输出插槽
    this.addSocket({
      name: 'scene',
      direction: SocketDirection.OUTPUT,
      type: SocketType.OBJECT,
      label: '生成的场景'
    });

    this.addSocket({
      name: 'understanding',
      direction: SocketDirection.OUTPUT,
      type: SocketType.OBJECT,
      label: '语言理解结果'
    });

    this.addSocket({
      name: 'progress',
      direction: SocketDirection.OUTPUT,
      type: SocketType.NUMBER,
      label: '生成进度'
    });

    this.addSocket({
      name: 'metadata',
      direction: SocketDirection.OUTPUT,
      type: SocketType.OBJECT,
      label: '元数据'
    });

    // 流程插槽
    this.addFlowSocket('success', SocketDirection.OUTPUT, '成功');
    this.addFlowSocket('error', SocketDirection.OUTPUT, '失败');
    this.addFlowSocket('progress', SocketDirection.OUTPUT, '进度更新');
  }

  async execute(): Promise<any> {
    const text = this.getInputValue('text') as string;
    const style = this.getInputValue('style') as string;
    const quality = this.getInputValue('quality') as number;
    const maxObjects = this.getInputValue('maxObjects') as number;

    // 验证输入
    if (!text || text.trim().length === 0) {
      this.setOutputValue('error', '场景描述不能为空');
      this.triggerFlow('error');
      return { success: false, error: '场景描述不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      const options: GenerationOptions = {
        style: style as any,
        quality,
        maxObjects,
        constraints: {
          maxPolygons: quality * 1000,
          targetFrameRate: 60
        },
        onProgress: (progress: number) => {
          this.setOutputValue('progress', progress);
          this.triggerFlow('progress');
        }
      };

      // 开始生成
      this.setOutputValue('progress', 0);

      const scene = await nlpGenerator.generateSceneFromNaturalLanguage(text, options);

      // 设置输出值
      this.setOutputValue('scene', scene);
      this.setOutputValue('progress', 100);

      // 如果有理解结果，也输出
      if (scene.understanding) {
        this.setOutputValue('understanding', scene.understanding);
      }

      // 设置元数据
      this.setOutputValue('metadata', {
        generationTime: Date.now(),
        objectCount: scene.entities?.length || 0,
        style,
        quality
      });

      this.triggerFlow('success');
      return { success: true, scene };

    } catch (error) {
      console.error('自然语言场景生成失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#722ED1',
      icon: 'robot',
      category: 'AI',
      tags: ['nlp', 'scene', 'generation', 'ai'],
      examples: [
        {
          name: '基础场景生成',
          description: '使用简单描述生成场景',
          inputs: {
            text: '创建一个现代办公室',
            style: 'realistic',
            quality: 70,
            maxObjects: 30
          }
        },
        {
          name: '复杂场景生成',
          description: '使用详细描述生成复杂场景',
          inputs: {
            text: '创建一个温馨的咖啡厅，有木质桌椅、暖色灯光和绿色植物',
            style: 'realistic',
            quality: 90,
            maxObjects: 60
          }
        }
      ]
    };
  }
}
```

### 4.2 创建场景理解节点

```typescript
// engine/src/visualscript/nodes/SceneUnderstandingNode.ts
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

export class SceneUnderstandingNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/scene/understand',
      category: NodeCategory.AI,
      name: '场景理解',
      description: '理解自然语言中的场景描述'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addSocket({
      name: 'text',
      direction: SocketDirection.INPUT,
      type: SocketType.STRING,
      label: '输入文本',
      required: true
    });

    // 输出插槽
    this.addSocket({
      name: 'entities',
      direction: SocketDirection.OUTPUT,
      type: SocketType.ARRAY,
      label: '识别的实体'
    });

    this.addSocket({
      name: 'sentiment',
      direction: SocketDirection.OUTPUT,
      type: SocketType.STRING,
      label: '情感倾向'
    });

    this.addSocket({
      name: 'intent',
      direction: SocketDirection.OUTPUT,
      type: SocketType.STRING,
      label: '意图'
    });

    this.addSocket({
      name: 'keywords',
      direction: SocketDirection.OUTPUT,
      type: SocketType.ARRAY,
      label: '关键词'
    });

    this.addSocket({
      name: 'style',
      direction: SocketDirection.OUTPUT,
      type: SocketType.STRING,
      label: '推断风格'
    });

    // 流程插槽
    this.addFlowSocket('success', SocketDirection.OUTPUT, '成功');
    this.addFlowSocket('error', SocketDirection.OUTPUT, '失败');
  }

  async execute(): Promise<any> {
    const text = this.getInputValue('text') as string;

    if (!text || text.trim().length === 0) {
      this.setOutputValue('error', '输入文本不能为空');
      this.triggerFlow('error');
      return { success: false, error: '输入文本不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      // 调用理解方法（需要在NLPSceneGenerator中暴露）
      const understanding = await nlpGenerator.understandText(text);

      // 设置输出值
      this.setOutputValue('entities', understanding.entities);
      this.setOutputValue('sentiment', understanding.sentiment);
      this.setOutputValue('intent', understanding.intent);
      this.setOutputValue('keywords', understanding.keywords);
      this.setOutputValue('style', understanding.style);

      this.triggerFlow('success');
      return { success: true, understanding };

    } catch (error) {
      console.error('文本理解失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  getConfiguration(): any {
    return {
      color: '#52C41A',
      icon: 'eye',
      category: 'AI',
      tags: ['nlp', 'understanding', 'analysis']
    };
  }
}
```

### 4.3 注册节点到视觉脚本系统

```typescript
// engine/src/visualscript/presets/NLPNodes.ts
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';
import { NLPSceneGenerationNode } from '../nodes/NLPSceneGenerationNode';
import { SceneUnderstandingNode } from '../nodes/SceneUnderstandingNode';

/**
 * 注册自然语言处理相关节点
 */
export function registerNLPNodes(registry: NodeRegistry): void {
  // 注册自然语言场景生成节点
  registry.registerNodeType({
    type: 'nlp/scene/generate',
    category: NodeCategory.AI,
    constructor: NLPSceneGenerationNode,
    label: '自然语言场景生成',
    description: '基于自然语言描述生成3D场景',
    icon: 'robot',
    color: '#722ED1',
    tags: ['nlp', 'scene', 'generation', 'ai'],
    version: '1.0.0',
    author: 'DL Engine Team'
  });

  // 注册场景理解节点
  registry.registerNodeType({
    type: 'nlp/scene/understand',
    category: NodeCategory.AI,
    constructor: SceneUnderstandingNode,
    label: '场景理解',
    description: '理解自然语言中的场景描述',
    icon: 'eye',
    color: '#52C41A',
    tags: ['nlp', 'understanding', 'analysis'],
    version: '1.0.0',
    author: 'DL Engine Team'
  });

  console.log('已注册自然语言处理节点');
}

/**
 * 获取NLP节点库信息
 */
export function getNLPNodeLibrary(): any {
  return {
    name: '自然语言处理',
    description: '提供自然语言理解和场景生成功能',
    version: '1.0.0',
    nodes: [
      {
        type: 'nlp/scene/generate',
        name: '自然语言场景生成',
        category: 'AI',
        complexity: 'advanced',
        performance: 'heavy'
      },
      {
        type: 'nlp/scene/understand',
        name: '场景理解',
        category: 'AI',
        complexity: 'intermediate',
        performance: 'medium'
      }
    ]
  };
}
```

### 4.4 集成到视觉脚本系统

```typescript
// engine/src/visualscript/VisualScriptSystem.ts
import { registerNLPNodes } from './presets/NLPNodes';

export class VisualScriptSystem extends System {
  // ... 其他代码

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // ... 其他初始化代码

    // 注册默认节点
    this.registerDefaultNodes();

    // 注册NLP节点
    registerNLPNodes(this.defaultNodeRegistry);

    this.initialized = true;
    this.eventEmitter.emit('initialized');
  }

  // ... 其他代码
}
```

## 五、配置和部署

### 5.1 环境配置

```typescript
// server/nlp-scene-service/.env
NODE_ENV=development
PORT=3009

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=dlengine

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# AI模型服务配置
AI_MODEL_ENDPOINT=http://localhost:8000
AI_MODEL_API_KEY=your_api_key

# 文件存储配置
STORAGE_TYPE=local
STORAGE_PATH=./storage/scenes
STORAGE_BASE_URL=http://localhost:3009/storage

# 日志配置
LOG_LEVEL=debug
LOG_FILE=./logs/nlp-scene-service.log

# 安全配置
JWT_SECRET=your_jwt_secret
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# 性能配置
CACHE_TTL=3600000
MAX_CONCURRENT_GENERATIONS=5
GENERATION_TIMEOUT=300000
```

### 5.2 Docker配置

```dockerfile
# server/nlp-scene-service/Dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    python3 \
    py3-pip \
    build-base \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# 创建必要目录
RUN mkdir -p /app/storage/scenes /app/logs
RUN chown -R nestjs:nodejs /app

USER nestjs

# 暴露端口
EXPOSE 3009

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3009/health || exit 1

# 启动命令
CMD ["npm", "run", "start:prod"]
```

### 5.3 Docker Compose配置

```yaml
# docker-compose.nlp.yml
version: '3.8'

services:
  nlp-scene-service:
    build:
      context: ./server/nlp-scene-service
      dockerfile: Dockerfile
    ports:
      - "3009:3009"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=dlengine
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=dlengine
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - AI_MODEL_ENDPOINT=http://ai-model-service:8000
      - STORAGE_TYPE=local
      - STORAGE_PATH=/app/storage/scenes
    volumes:
      - nlp_scene_storage:/app/storage/scenes
      - nlp_scene_logs:/app/logs
    depends_on:
      - mysql
      - redis
      - ai-model-service
    networks:
      - dlengine-network
    restart: unless-stopped

  ai-model-service:
    image: tensorflow/serving:latest
    ports:
      - "8000:8501"
    environment:
      - MODEL_NAME=nlp_scene_model
      - MODEL_BASE_PATH=/models
    volumes:
      - ./ai-models:/models
    networks:
      - dlengine-network
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
      - MYSQL_DATABASE=dlengine
      - MYSQL_USER=dlengine
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - dlengine-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - dlengine-network
    restart: unless-stopped

volumes:
  nlp_scene_storage:
  nlp_scene_logs:
  mysql_data:
  redis_data:

networks:
  dlengine-network:
    external: true
```

### 5.4 启动脚本

```bash
#!/bin/bash
# scripts/start-nlp-scene-service.sh

echo "启动自然语言场景生成服务..."

# 检查环境变量
if [ -z "$DB_PASSWORD" ]; then
    echo "错误: DB_PASSWORD 环境变量未设置"
    exit 1
fi

# 创建网络（如果不存在）
docker network create dlengine-network 2>/dev/null || true

# 启动服务
docker-compose -f docker-compose.nlp.yml up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "检查服务状态..."
curl -f http://localhost:3009/health || {
    echo "服务启动失败"
    docker-compose -f docker-compose.nlp.yml logs nlp-scene-service
    exit 1
}

echo "自然语言场景生成服务启动成功!"
echo "服务地址: http://localhost:3009"
echo "API文档: http://localhost:3009/api/docs"
```

## 六、使用示例

### 6.1 基础使用示例

```typescript
// examples/basic-nlp-scene-generation.ts
import { Engine } from '@dl-engine/core';
import { NLPSceneGenerator } from '@dl-engine/ai';

async function basicExample() {
  // 1. 初始化引擎
  const engine = new Engine();
  await engine.initialize();

  // 2. 获取自然语言场景生成器
  const nlpGenerator = engine.getSystem('NLPSceneGenerator');

  // 3. 生成场景
  const scene = await nlpGenerator.generateSceneFromNaturalLanguage(
    '创建一个现代化的图书馆，有高大的书架、舒适的阅读区和自然光照',
    {
      style: 'realistic',
      quality: 80,
      maxObjects: 50,
      constraints: {
        maxPolygons: 100000,
        targetFrameRate: 60
      },
      onProgress: (progress) => {
        console.log(`生成进度: ${progress}%`);
      }
    }
  );

  // 4. 将场景添加到世界中
  const world = engine.getWorld();
  world.addScene(scene);

  console.log('场景生成完成!');
  console.log(`场景包含 ${scene.entities.length} 个实体`);
}

basicExample().catch(console.error);
```

### 6.2 编辑器集成示例

```typescript
// examples/editor-integration.ts
import React, { useEffect, useState } from 'react';
import { NLPSceneGenerationPanel } from '../components/panels/NLPSceneGenerationPanel';
import { useEngineService } from '../hooks/useEngineService';

export const EditorWithNLP: React.FC = () => {
  const [isNLPPanelVisible, setIsNLPPanelVisible] = useState(false);
  const engineService = useEngineService();

  useEffect(() => {
    // 监听场景生成事件
    const engine = engineService.getEngine();
    const nlpGenerator = engine?.getSystem('NLPSceneGenerator');

    if (nlpGenerator) {
      nlpGenerator.addEventListener('sceneGenerated', (event) => {
        console.log('新场景已生成:', event.scene);
        // 可以在这里添加场景到编辑器
      });
    }
  }, [engineService]);

  return (
    <div className="editor-layout">
      <div className="editor-toolbar">
        <button
          onClick={() => setIsNLPPanelVisible(!isNLPPanelVisible)}
          className="toolbar-button"
        >
          AI场景生成
        </button>
      </div>

      <div className="editor-content">
        {isNLPPanelVisible && (
          <div className="side-panel">
            <NLPSceneGenerationPanel />
          </div>
        )}

        <div className="main-viewport">
          {/* 主视口内容 */}
        </div>
      </div>
    </div>
  );
};
```

### 6.3 视觉脚本使用示例

```typescript
// examples/visual-script-nlp.ts
import { VisualScriptEngine } from '@dl-engine/visual-script';

async function visualScriptExample() {
  const scriptEngine = new VisualScriptEngine();

  // 创建包含NLP节点的脚本
  const script = {
    nodes: [
      {
        id: 'input',
        type: 'core/input/string',
        position: { x: 100, y: 100 },
        data: {
          value: '创建一个温馨的咖啡厅'
        }
      },
      {
        id: 'nlp_generate',
        type: 'nlp/scene/generate',
        position: { x: 300, y: 100 },
        data: {
          style: 'realistic',
          quality: 80,
          maxObjects: 40
        }
      },
      {
        id: 'output',
        type: 'core/output/object',
        position: { x: 500, y: 100 }
      }
    ],
    connections: [
      {
        from: { nodeId: 'input', socketName: 'value' },
        to: { nodeId: 'nlp_generate', socketName: 'text' }
      },
      {
        from: { nodeId: 'nlp_generate', socketName: 'scene' },
        to: { nodeId: 'output', socketName: 'value' }
      }
    ]
  };

  // 执行脚本
  const result = await scriptEngine.execute(script);
  console.log('生成的场景:', result.outputs.value);
}

visualScriptExample().catch(console.error);
```

## 总结

本技术实现指南详细介绍了 如何在DL引擎中实现自然语言生成场景功能，包括：

1. **引擎层实现**：核心的NLPSceneGenerator系统和相关组件
2. **编辑器界面**：用户友好的生成面板和预览组件
3. **服务器端**：完整的API服务和数据管理
4. **视觉脚本集成**：可视化编程节点和注册机制
5. **配置部署**：Docker容器化和环境配置
6. **使用示例**：各种使用场景的代码示例

该实现方案充分利用了现有的DL引擎架构，确保了功能的完整性和系统的一致性，为用户提供了强大的自然语言场景生成能力。