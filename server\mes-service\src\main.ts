import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('MESService');

  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // 启用CORS
  app.enableCors({
    origin: true,
    credentials: true,
  });

  // 设置全局前缀
  app.setGlobalPrefix('api/v1');

  // 配置Swagger文档
  const config = new DocumentBuilder()
    .setTitle('制造执行系统(MES) API')
    .setDescription('智慧工厂制造执行系统的API文档')
    .setVersion('1.0')
    .addTag('orders', '生产订单管理')
    .addTag('processes', '工艺路线管理')
    .addTag('quality', '质量管理')
    .addTag('inventory', '库存管理')
    .addTag('scheduling', '生产调度')
    .addTag('tracking', '生产跟踪')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  const port = configService.get<number>('PORT', 3008);
  
  await app.listen(port);
  
  logger.log(`制造执行系统(MES)服务已启动，端口: ${port}`);
  logger.log(`API文档地址: http://localhost:${port}/api/docs`);
  logger.log(`服务地址: http://localhost:${port}/api/v1`);
}

bootstrap().catch(error => {
  console.error('启动MES服务失败:', error);
  process.exit(1);
});
